#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 Coupon 项目适配功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from internal.storage.models import MerchantInfo, SiteConfig
from internal.utils.slugutil import title_to_slug, generate_unique_slug, validate_slug
from internal.publisher.publisher import CouponsPostgresPublisher
from datetime import datetime


def test_merchant_info_with_featured():
    """测试 MerchantInfo 模型的 featured 字段"""
    print("=== 测试 MerchantInfo 模型的 featured 字段 ===")
    
    # 测试数据
    test_data = [
        {
            'id': '1',
            'brand_slug': 'test-brand',
            'brand_name': 'Test Brand',
            'categories': 'Electronics,Gadgets',
            'country': 'US',
            'supported_countries': 'US,CA,UK',
            'official_url': 'https://example.com',
            'publish_date': '2024-01-01 10:00:00',
            'affiliate_url': 'https://affiliate.example.com',
            'site_id': 'test_site',
            'featured': 'TRUE'  # 字符串形式
        },
        {
            'id': '2',
            'brand_slug': 'test-brand-2',
            'brand_name': 'Test Brand 2',
            'categories': 'Fashion',
            'country': 'UK',
            'supported_countries': 'UK,EU',
            'official_url': 'https://example2.com',
            'publish_date': '2024-01-02 11:00:00',
            'affiliate_url': 'https://affiliate.example2.com',
            'site_id': 'test_site',
            'featured': False  # 布尔形式
        }
    ]
    
    for i, data in enumerate(test_data, 1):
        try:
            merchant = MerchantInfo.from_dict(data)
            print(f"测试 {i}: ✓")
            print(f"  品牌名: {merchant.brand_name}")
            print(f"  Featured: {merchant.featured} (类型: {type(merchant.featured)})")
            print(f"  分类: {merchant.categories}")
        except Exception as e:
            print(f"测试 {i}: ✗ 错误: {str(e)}")
    
    print()


def test_slug_generation():
    """测试 slug 生成功能"""
    print("=== 测试 slug 生成功能 ===")
    
    test_titles = [
        "Best Deals at Amazon - Save Big Today!",
        "中文标题测试",
        "Mixed 中英文 Title with Special@#Characters",
        "Very Long Title That Should Be Truncated Because It Exceeds The Maximum Length Limit",
        "Simple Title",
        ""  # 空标题
    ]
    
    for title in test_titles:
        slug = title_to_slug(title)
        is_valid = validate_slug(slug)
        print(f"标题: '{title}'")
        print(f"Slug: '{slug}'")
        print(f"有效: {is_valid}")
        print("-" * 40)
    
    print()


def test_deduplication_logic():
    """测试去重逻辑"""
    print("=== 测试去重逻辑 ===")
    
    # 模拟重复的商家数据
    test_merchants = [
        MerchantInfo(
            id='1', brand_slug='brand-1', brand_name='Brand 1',
            website_screenshot='', categories=['Electronics'],
            country='US', supported_countries=['US'],
            official_url='https://example.com', 
            publish_date=datetime.now(),
            affiliate_url='https://aff1.com', site_id='site1',
            featured=True
        ),
        MerchantInfo(
            id='2', brand_slug='brand-2', brand_name='Brand 2',
            website_screenshot='', categories=['Electronics'],
            country='US', supported_countries=['US'],
            official_url='https://example.com/',  # 相同域名，但有尾部斜杠
            publish_date=datetime.now(),
            affiliate_url='https://aff2.com', site_id='site1',
            featured=False
        ),
        MerchantInfo(
            id='3', brand_slug='brand-3', brand_name='Brand 3',
            website_screenshot='', categories=['Fashion'],
            country='UK', supported_countries=['UK'],
            official_url='https://different.com',
            publish_date=datetime.now(),
            affiliate_url='https://aff3.com', site_id='site1',
            featured=True
        )
    ]
    
    # 模拟去重逻辑
    from internal.engine import AriticEngine
    
    # 创建一个临时的 AriticEngine 实例来测试去重方法
    # 注意：这里我们不能直接实例化，因为需要凭证，所以我们直接测试去重逻辑
    seen_urls = set()
    deduplicated_merchants = []
    
    for merchant in test_merchants:
        # 标准化URL
        normalized_url = merchant.official_url.lower().strip()
        if normalized_url.startswith('http://'):
            normalized_url = normalized_url[7:]
        elif normalized_url.startswith('https://'):
            normalized_url = normalized_url[8:]
        normalized_url = normalized_url.rstrip('/')
        
        if normalized_url not in seen_urls:
            seen_urls.add(normalized_url)
            deduplicated_merchants.append(merchant)
            print(f"保留: {merchant.brand_name} (URL: {merchant.official_url})")
        else:
            print(f"去重: {merchant.brand_name} (URL: {merchant.official_url}) - URL已存在")
    
    print(f"原始商家数量: {len(test_merchants)}")
    print(f"去重后商家数量: {len(deduplicated_merchants)}")
    print()


def test_site_config():
    """测试站点配置"""
    print("=== 测试站点配置 ===")
    
    # 测试 couponspostgres 配置
    site_config_data = {
        'site_id': 'coupon_site',
        'site_type': 'couponspostgres',
        'site_url': 'localhost:5432',
        'site_api': 'coupon_db',
        'site_username': 'postgres',
        'site_password': 'password'
    }
    
    try:
        site_config = SiteConfig.from_dict(site_config_data)
        print(f"站点配置创建成功:")
        print(f"  站点ID: {site_config.site_id}")
        print(f"  站点类型: {site_config.site_type}")
        print(f"  数据库URL: {site_config.site_url}")
        print(f"  数据库名: {site_config.site_api}")
        print("✓ 站点配置测试通过")
    except Exception as e:
        print(f"✗ 站点配置测试失败: {str(e)}")
    
    print()


def main():
    """主测试函数"""
    print("开始测试 Coupon 项目适配功能...\n")
    
    test_merchant_info_with_featured()
    test_slug_generation()
    test_deduplication_logic()
    test_site_config()
    
    print("所有测试完成！")
    print("\n=== 使用说明 ===")
    print("1. 在 Google Sheets 的 merchant_info 工作表中，site_id 列后面添加 featured 列")
    print("2. featured 列的值设置为 TRUE 或 FALSE")
    print("3. 在 site_config 工作表中，将 site_type 设置为 'couponspostgres'")
    print("4. 确保数据库连接信息正确配置")
    print("5. categories 字段现在应该包含分类ID而不是分类名称")
    print("6. 系统会自动按 official_url 去重，相同域名的品牌只保留一个")


if __name__ == "__main__":
    main()
