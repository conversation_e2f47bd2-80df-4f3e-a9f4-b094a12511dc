#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from internal.engine import AriticEngine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs', 'aritic.log')),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def main():
    """主程序入口"""
    try:
        # 创建日志目录
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)

        # 获取配置文件路径
        credentials_path = 'credentials.json'

        spreadsheet_name = 'affiliate-content'

        # 初始化并运行Aritic Engine
        engine = AriticEngine(credentials_path, spreadsheet_name)
        engine.run()
    except Exception as e:
        logger.error(f"运行Aritic Engine时出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
