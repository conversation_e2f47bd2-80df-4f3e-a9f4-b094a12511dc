# Coupon 项目适配说明

本文档说明了 aritic-engine 项目为适配 coupon 项目所做的修改和配置。

## 主要修改

### 1. 数据模型更新

#### MerchantInfo 模型 (`internal/storage/models.py`)
- **新增字段**: `featured: bool`
- **功能**: 支持从 Google Sheets 读取 featured 列的值
- **支持格式**: 
  - 字符串: "TRUE"/"FALSE" (不区分大小写)
  - 布尔值: True/False
  - 其他值会转换为布尔值

```python
# 示例数据
merchant_data = {
    'id': '1',
    'brand_slug': 'example-brand',
    'brand_name': 'Example Brand',
    # ... 其他字段
    'featured': 'TRUE'  # 新增的 featured 字段
}
```

### 2. 品牌数据去重功能

#### AriticEngine 类 (`internal/engine.py`)
- **新增方法**: `_deduplicate_merchants_by_url()`
- **功能**: 按 `official_url` 去重，相同域名的品牌只保留一个
- **去重逻辑**:
  - 标准化 URL（去除协议前缀和尾部斜杠）
  - 不区分大小写
  - 保留第一个遇到的品牌，删除后续重复的

```python
# 示例：这两个 URL 会被认为是重复的
# https://example.com
# https://example.com/
# http://example.com
```

### 3. Slug 生成工具

#### 新文件: `internal/utils/slugutil.py`
- **主要函数**:
  - `title_to_slug(title)`: 将标题转换为 slug
  - `generate_unique_slug(title, db_engine, existing_slugs)`: 生成唯一 slug
  - `validate_slug(slug)`: 验证 slug 格式
  - `sanitize_slug(slug)`: 清理和标准化 slug

**特性**:
- 支持中英文混合标题
- 自动处理特殊字符
- 长度限制（最大 250 字符）
- 唯一性检查（数据库 + 内存）
- 自动添加数字后缀避免重复

### 4. CouponsPostgresPublisher 发布器

#### 新发布器类 (`internal/publisher/publisher.py`)
- **类名**: `CouponsPostgresPublisher`
- **功能**: 专门适配 coupon 项目的 articles 表结构
- **支持字段**:
  - `slug`: 自动生成唯一 slug
  - `featured`: 从 merchant_info.featured 获取
  - `category_id`: 从 category_name 参数获取（现在传入分类ID）
  - `brand_id`: 从 merchant_info.id 获取
  - 其他标准字段

**数据库表结构适配**:
```sql
INSERT INTO articles (
    slug, title, description, content, featured_image,
    meta_title, meta_desc, author_name, featured, published,
    brand_id, category_id, published_at, created_at, updated_at
) VALUES (...)
```

## 配置说明

### 1. Google Sheets 配置

#### merchant_info 工作表
在 `site_id` 列后面添加新列：
- **列名**: `featured`
- **值**: `TRUE` 或 `FALSE`

示例表格结构：
```
| id | brand_slug | brand_name | ... | site_id | featured |
|----|------------|------------|-----|---------|----------|
| 1  | brand-1    | Brand 1    | ... | site1   | TRUE     |
| 2  | brand-2    | Brand 2    | ... | site1   | FALSE    |
```

#### site_config 工作表
- **site_type**: 设置为 `couponspostgres`
- **site_url**: 数据库地址 (如: `localhost:5432`)
- **site_api**: 数据库名称
- **site_username**: 数据库用户名
- **site_password**: 数据库密码

### 2. 分类配置变更

**重要变更**: `categories` 字段现在应该包含**分类ID**而不是分类名称。

- **之前**: `categories: "Electronics,Fashion"`
- **现在**: `categories: "1,2"` (对应的分类ID)

这样可以直接将分类ID传入数据库，避免名称到ID的转换。

## 使用方法

### 1. 运行测试
```bash
cd aritic-engine
python test_coupon_adaptation.py
```

### 2. 正常使用
1. 确保 Google Sheets 配置正确
2. 在 site_config 中设置 `site_type` 为 `couponspostgres`
3. 配置正确的数据库连接信息
4. 运行 aritic-engine

### 3. 发布器选择
系统会根据 `site_config.site_type` 自动选择发布器：
- `wordpress`: WordPressPublisher
- `custom`: CustomAPIPublisher  
- `postgres`: PostgresPublisher
- `couponspostgres`: CouponsPostgresPublisher (新增)

## 数据流程

1. **读取数据**: 从 Google Sheets 读取 merchant_info，包括新的 featured 字段
2. **去重处理**: 按 official_url 去重，相同域名只保留一个品牌
3. **生成文章**: 使用现有的文章生成逻辑
4. **发布文章**: 使用 CouponsPostgresPublisher 发布到 coupon 项目数据库
   - 自动生成唯一 slug
   - 设置 featured 状态
   - 处理分类ID和品牌ID
   - 插入到 articles 表

## 注意事项

1. **数据库依赖**: 需要安装 `sqlalchemy` 和 `psycopg2` 包
2. **分类ID**: 确保 Google Sheets 中的 categories 字段包含有效的分类ID
3. **品牌ID**: merchant_info.id 应该是有效的数字ID
4. **数据库权限**: 确保数据库用户有 articles 表的插入权限
5. **Slug 唯一性**: 系统会自动处理 slug 重复问题，添加数字后缀

## 错误处理

- **数据库连接失败**: 检查连接配置和网络
- **Slug 生成失败**: 会使用随机字符串作为后备方案
- **字段类型错误**: 会使用默认值并记录警告日志
- **去重失败**: 不会影响整体流程，会记录错误日志

## 测试建议

1. 先在测试环境验证配置
2. 检查生成的 slug 是否符合预期
3. 验证 featured 字段是否正确设置
4. 确认分类ID和品牌ID的映射关系
5. 测试去重逻辑是否正常工作
