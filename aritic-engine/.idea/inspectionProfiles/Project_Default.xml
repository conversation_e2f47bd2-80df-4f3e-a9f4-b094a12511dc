<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="JsonStandardCompliance" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="133">
            <item index="0" class="java.lang.String" itemvalue="g4f" />
            <item index="1" class="java.lang.String" itemvalue="tiktoken" />
            <item index="2" class="java.lang.String" itemvalue="numba" />
            <item index="3" class="java.lang.String" itemvalue="more-itertools" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="llvmlite" />
            <item index="6" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="Jinja2" />
            <item index="9" class="java.lang.String" itemvalue="APScheduler" />
            <item index="10" class="java.lang.String" itemvalue="fsspec" />
            <item index="11" class="java.lang.String" itemvalue="tqdm" />
            <item index="12" class="java.lang.String" itemvalue="schedule" />
            <item index="13" class="java.lang.String" itemvalue="regex" />
            <item index="14" class="java.lang.String" itemvalue="selenium" />
            <item index="15" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="16" class="java.lang.String" itemvalue="pytz" />
            <item index="17" class="java.lang.String" itemvalue="urllib3" />
            <item index="18" class="java.lang.String" itemvalue="idna" />
            <item index="19" class="java.lang.String" itemvalue="fastapi" />
            <item index="20" class="java.lang.String" itemvalue="PyYAML" />
            <item index="21" class="java.lang.String" itemvalue="certifi" />
            <item index="22" class="java.lang.String" itemvalue="anyio" />
            <item index="23" class="java.lang.String" itemvalue="nodriver" />
            <item index="24" class="java.lang.String" itemvalue="aiohttp" />
            <item index="25" class="java.lang.String" itemvalue="aiohttp-socks" />
            <item index="26" class="java.lang.String" itemvalue="uvicorn" />
            <item index="27" class="java.lang.String" itemvalue="httpx" />
            <item index="28" class="java.lang.String" itemvalue="browser-cookie3" />
            <item index="29" class="java.lang.String" itemvalue="curl_cffi" />
            <item index="30" class="java.lang.String" itemvalue="greenlet" />
            <item index="31" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="32" class="java.lang.String" itemvalue="pycparser" />
            <item index="33" class="java.lang.String" itemvalue="gitdb" />
            <item index="34" class="java.lang.String" itemvalue="unidiff" />
            <item index="35" class="java.lang.String" itemvalue="Pygments" />
            <item index="36" class="java.lang.String" itemvalue="pycryptodomex" />
            <item index="37" class="java.lang.String" itemvalue="starlette" />
            <item index="38" class="java.lang.String" itemvalue="soupsieve" />
            <item index="39" class="java.lang.String" itemvalue="ttkbootstrap" />
            <item index="40" class="java.lang.String" itemvalue="GitPython" />
            <item index="41" class="java.lang.String" itemvalue="transformers" />
            <item index="42" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="43" class="java.lang.String" itemvalue="python-engineio" />
            <item index="44" class="java.lang.String" itemvalue="asgiref" />
            <item index="45" class="java.lang.String" itemvalue="pexpect" />
            <item index="46" class="java.lang.String" itemvalue="loguru" />
            <item index="47" class="java.lang.String" itemvalue="psutil" />
            <item index="48" class="java.lang.String" itemvalue="openai" />
            <item index="49" class="java.lang.String" itemvalue="platformdirs" />
            <item index="50" class="java.lang.String" itemvalue="propcache" />
            <item index="51" class="java.lang.String" itemvalue="fastcore" />
            <item index="52" class="java.lang.String" itemvalue="Flask-SocketIO" />
            <item index="53" class="java.lang.String" itemvalue="llama-index-embeddings-huggingface" />
            <item index="54" class="java.lang.String" itemvalue="httpcore" />
            <item index="55" class="java.lang.String" itemvalue="ghapi" />
            <item index="56" class="java.lang.String" itemvalue="smmap" />
            <item index="57" class="java.lang.String" itemvalue="cffi" />
            <item index="58" class="java.lang.String" itemvalue="datasets" />
            <item index="59" class="java.lang.String" itemvalue="pyasn1" />
            <item index="60" class="java.lang.String" itemvalue="bidict" />
            <item index="61" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="62" class="java.lang.String" itemvalue="ruamel.yaml.clib" />
            <item index="63" class="java.lang.String" itemvalue="zipp" />
            <item index="64" class="java.lang.String" itemvalue="tenacity" />
            <item index="65" class="java.lang.String" itemvalue="mmh3" />
            <item index="66" class="java.lang.String" itemvalue="async-property" />
            <item index="67" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="68" class="java.lang.String" itemvalue="websockets" />
            <item index="69" class="java.lang.String" itemvalue="blinker" />
            <item index="70" class="java.lang.String" itemvalue="pyarrow" />
            <item index="71" class="java.lang.String" itemvalue="annotated-types" />
            <item index="72" class="java.lang.String" itemvalue="importlib_metadata" />
            <item index="73" class="java.lang.String" itemvalue="scipy" />
            <item index="74" class="java.lang.String" itemvalue="Flask-Cors" />
            <item index="75" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="76" class="java.lang.String" itemvalue="pyasn1_modules" />
            <item index="77" class="java.lang.String" itemvalue="waitress" />
            <item index="78" class="java.lang.String" itemvalue="lz4" />
            <item index="79" class="java.lang.String" itemvalue="asyncstdlib" />
            <item index="80" class="java.lang.String" itemvalue="python-multipart" />
            <item index="81" class="java.lang.String" itemvalue="cachetools" />
            <item index="82" class="java.lang.String" itemvalue="multidict" />
            <item index="83" class="java.lang.String" itemvalue="google-api-python-client" />
            <item index="84" class="java.lang.String" itemvalue="yarl" />
            <item index="85" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="86" class="java.lang.String" itemvalue="swe-rex" />
            <item index="87" class="java.lang.String" itemvalue="pyjsparser" />
            <item index="88" class="java.lang.String" itemvalue="protobuf" />
            <item index="89" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="90" class="java.lang.String" itemvalue="docstring_parser" />
            <item index="91" class="java.lang.String" itemvalue="googleapis-common-protos" />
            <item index="92" class="java.lang.String" itemvalue="jiter" />
            <item index="93" class="java.lang.String" itemvalue="duckduckgo_search" />
            <item index="94" class="java.lang.String" itemvalue="setuptools" />
            <item index="95" class="java.lang.String" itemvalue="frozenlist" />
            <item index="96" class="java.lang.String" itemvalue="Faker" />
            <item index="97" class="java.lang.String" itemvalue="Js2Py" />
            <item index="98" class="java.lang.String" itemvalue="safetensors" />
            <item index="99" class="java.lang.String" itemvalue="rich-argparse" />
            <item index="100" class="java.lang.String" itemvalue="PyExecJS" />
            <item index="101" class="java.lang.String" itemvalue="pyparsing" />
            <item index="102" class="java.lang.String" itemvalue="google-api-core" />
            <item index="103" class="java.lang.String" itemvalue="litellm" />
            <item index="104" class="java.lang.String" itemvalue="tokenizers" />
            <item index="105" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="106" class="java.lang.String" itemvalue="cryptography" />
            <item index="107" class="java.lang.String" itemvalue="python-socketio" />
            <item index="108" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="109" class="java.lang.String" itemvalue="distro" />
            <item index="110" class="java.lang.String" itemvalue="simple-websocket" />
            <item index="111" class="java.lang.String" itemvalue="undetected-chromedriver" />
            <item index="112" class="java.lang.String" itemvalue="ptyprocess" />
            <item index="113" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="114" class="java.lang.String" itemvalue="simple-parsing" />
            <item index="115" class="java.lang.String" itemvalue="requests-oauthlib" />
            <item index="116" class="java.lang.String" itemvalue="py-arkose-generator" />
            <item index="117" class="java.lang.String" itemvalue="rpds-py" />
            <item index="118" class="java.lang.String" itemvalue="aiohttp_socks" />
            <item index="119" class="java.lang.String" itemvalue="primp" />
            <item index="120" class="java.lang.String" itemvalue="google-auth-httplib2" />
            <item index="121" class="java.lang.String" itemvalue="Flask" />
            <item index="122" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="123" class="java.lang.String" itemvalue="bashlex" />
            <item index="124" class="java.lang.String" itemvalue="ruamel.yaml" />
            <item index="125" class="java.lang.String" itemvalue="rich" />
            <item index="126" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="127" class="java.lang.String" itemvalue="zstandard" />
            <item index="128" class="java.lang.String" itemvalue="proto-plus" />
            <item index="129" class="java.lang.String" itemvalue="pillow" />
            <item index="130" class="java.lang.String" itemvalue="google-auth" />
            <item index="131" class="java.lang.String" itemvalue="Pillow" />
            <item index="132" class="java.lang.String" itemvalue="requests" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>