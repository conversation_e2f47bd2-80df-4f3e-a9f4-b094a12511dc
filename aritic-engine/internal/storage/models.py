from dataclasses import dataclass
from typing import Any, Dict, Optional, List
from datetime import datetime
from internal.generator import image

@dataclass
class MerchantInfo:
    """商家信息数据模型"""
    id: str
    brand_slug: str
    brand_name: str
    website_screenshot: str
    categories: List[str]
    country: str
    supported_countries: List[str]
    official_url: str
    publish_date: datetime
    affiliate_url: str
    site_id: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MerchantInfo':
        return cls(
            id=data['id'],
            brand_slug=data['brand_slug'],
            brand_name=data['brand_name'],
            categories=data['categories'].split(',') if isinstance(data['categories'], str) else data['categories'],
            country=data['country'],
            supported_countries=data['supported_countries'].split(',') if isinstance(data['supported_countries'], str) else data['supported_countries'],
            official_url=data['official_url'],
            publish_date=datetime.fromisoformat(data['publish_date']) if isinstance(data['publish_date'], str) else data['publish_date'],
            affiliate_url=data['affiliate_url'],
            site_id=data['site_id'],
            website_screenshot=image.get_website_screenshot(data['official_url'])
        )

@dataclass
class SiteConfig:
    """站点配置数据模型"""
    site_id: str
    site_type: str
    site_url: str
    site_api: str
    site_username: str
    site_password: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SiteConfig':
        return cls(
            site_id=data['site_id'],
            site_type=data['site_type'],
            site_url=data['site_url'],
            site_api=data['site_api'],
            site_username=data['site_username'],
            site_password=data['site_password']
        )

@dataclass
class ProxyConfig:
    """代理配置数据模型"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    type: str = 'socks5'

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProxyConfig':
        return cls(
            host=data['host'],
            port=int(data['port']),
            username=data.get('username'),
            password=data.get('password'),
            type=data.get('type', 'socks5')
        )

@dataclass
class ArticleStatus:
    """文章状态数据模型"""
    brand_slug: str
    official_url: str
    site_id: str
    status: str
    timestamp: datetime

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ArticleStatus':
        return cls(
            brand_slug=data['brand_slug'],
            official_url=data['official_url'],
            site_id=data['site_id'],
            status=data['status'],
            timestamp=datetime.fromisoformat(data['timestamp']) if isinstance(data['timestamp'], str) else data['timestamp']
        )