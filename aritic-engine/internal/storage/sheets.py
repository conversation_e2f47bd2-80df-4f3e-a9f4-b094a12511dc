import gspread
from oauth2client.service_account import ServiceAccountCredentials
from typing import List, Dict, Any
import logging
from datetime import datetime
from .models import MerchantInfo, SiteConfig, ProxyConfig, ArticleStatus


class SheetService:
    def __init__(self, credentials_dict: Dict[str, Any], spreadsheet_name: str):
        """
        初始化SheetService
        :param credentials_dict: Google API凭证字典
        :param spreadsheet_name: Google Sheets文档名称
        """
        try:
            scopes = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
            creds = ServiceAccountCredentials.from_json_keyfile_dict(credentials_dict, scopes)
            self.client = gspread.authorize(creds)
            self.spreadsheet = self.client.open(spreadsheet_name)
            logging.info(f"成功初始化SheetService，spreadsheet_name: {spreadsheet_name}")
        except Exception as e:
            logging.error(f"初始化SheetService失败: {str(e)}")
            raise

    def _get_worksheet_data(self, worksheet_name: str) -> List[Dict[str, Any]]:
        """
        获取工作表数据的通用方法
        :param worksheet_name: 工作表名称
        :return: 包含表格数据的字典列表
        """
        try:
            worksheet = self.spreadsheet.worksheet(worksheet_name)
            data = worksheet.get_all_records()
            logging.info(f"成功获取{worksheet_name}工作表数据，共{len(data)}条记录")
            return data
        except Exception as e:
            logging.error(f"获取{worksheet_name}工作表数据失败: {str(e)}")
            raise

    def get_merchant_info(self) -> List[Dict[str, Any]]:
        """
        获取merchant_info工作表数据
        :return: 商家信息列表
        """
        return self._get_worksheet_data('merchant_info')

    def get_site_config(self) -> List[Dict[str, Any]]:
        """
        获取site_config工作表数据
        :return: 站点配置列表
        """
        return self._get_worksheet_data('site_config')

    def get_proxy_config(self) -> List[Dict[str, Any]]:
        """
        获取proxy_config工作表数据
        :return: 代理配置列表
        """
        return self._get_worksheet_data('proxy_config')

    def get_article_status(self) -> List[Dict[str, Any]]:
        """
        获取article_status工作表数据
        :return: 文章状态列表
        """
        return self._get_worksheet_data('article_status')

    def update_article_status(self, brand_slug: str, official_url: str, site_id: str, status: str) -> None:
        """
        更新文章状态
        :param brand_slug: 品牌标识
        :param official_url: 官方URL
        :param site_id: 站点ID
        :param status: 状态
        """
        try:
            worksheet = self.spreadsheet.worksheet('article_status')
            timestamp = datetime.now().isoformat()
            worksheet.append_row([brand_slug, official_url, site_id, status, timestamp])
            logging.info(f"成功更新文章状态: {brand_slug} - {site_id} - {status}")
        except Exception as e:
            logging.error(f"更新文章状态失败: {str(e)}")
            raise
