from datetime import datetime
from typing import List, Dict, Any, Optional
import json

from internal.generator import ai, image, marketing, reference, review
from internal.generator.reference import SearchResult
from internal.generator.review import Review
from internal.generator.image import ImageInfo


class ArticleData:
    """Article data structure containing all generated content"""

    def __init__(self):
        self.title: str = ""
        self.meta_description: str = ""
        self.content: str = ""
        self.tags: List[str] = []
        self.category_name: str = ""
        self.image_suggestion: str = ""
        self.website_logo: str = ""
        self.website_screenshot: str = ""
        self.marketing_button: List[str] = []
        self.reference_articles: List[SearchResult] = []
        self.reviews: List[Review] = []
        self.images: List[ImageInfo] = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert article data to dictionary format"""
        return {
            "title": self.title,
            "meta_description": self.meta_description,
            "content": self.content,
            "tags": self.tags,
            "category_name": self.category_name,
            "image_suggestion": self.image_suggestion,
            "website_logo": self.website_logo,
            "website_screenshot": self.website_screenshot,
            "marketing_button": self.marketing_button,
            "reference_articles": [
                {
                    "title": article.title,
                    "link": article.link,
                    "snippet": article.snippet,
                    "content": article.content
                } for article in self.reference_articles
            ],
            "reviews": [
                {
                    "platform": review.platform,
                    "rating": review.rating,
                    "content": review.content,
                    "author": review.author,
                    "date": review.date.isoformat() if review.date else None,
                    "url": review.url
                } for review in self.reviews
            ]
        }

    def to_json(self) -> str:
        """Convert article data to JSON string"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


class Integrator:
    def generate_article(self, site: str, affiliate_url: str, proxy_info: str = None) -> ArticleData:
        """Generate complete article
        
        Args:
            site: Website domain
            proxy_info: Proxy information
            
        Returns:
            ArticleData: Article data structure containing all generated content
        """
        article_data = ArticleData()

        # 1. 生成文本内容
        ai_content = ai.generate_article_content(site, proxy_info)
        if ai_content:
            article_data.title = ai_content.get("title", "")
            article_data.meta_description = ai_content.get("meta_description", "")
            article_data.content = ai_content.get("content", "")
            article_data.tags = ai_content.get("tags", [])
            article_data.category_name = ai_content.get("category_name", [])
            article_data.image_suggestion = ai_content.get("image_suggestion", "")

        # 2. Get website logo
        article_data.website_logo = image.get_website_logo(site)

        # 3. Get website screenshot
        article_data.website_screenshot = image.get_website_screenshot(site)

        # 4. Generate marketing button
        article_data.marketing_button = marketing.generate_buttons(affiliate_url, site)

        # 5. Get reference articles
        try:
            ref_config = reference.ReferenceConfig()
            ref = reference.Reference(ref_config)
            article_data.reference_articles = ref.search_web(site)
        except:
            article_data.reference_articles = []

        # 6. Get reviews
        try:
            reviews, error = review.get_trustpilot_reviews(site)
            if error:
                print(f"Error getting reviews: {error}")
            article_data.reviews = reviews
        except:
            article_data.reviews = []

        # 7. search image
        try:
            article_data.images = image.search_images(site, proxy_info)
        except:
            article_data.images = []
        # 8. Process and update content
        # Insert marketing buttons
        article_data.content = self._insert_buttons(article_data.content, article_data.marketing_button)

        # Insert images
        article_data.content = self._insert_images(article_data.content, article_data.images, affiliate_url)

        # Insert reviews
        reviews_content = self._insert_reviews(article_data.reviews)
        article_data.content = f"{article_data.content}\n\n{reviews_content}"

        # Insert references
        references_content = self._insert_references(article_data.reference_articles)
        article_data.content = f"{article_data.content}\n\n{references_content}"

        return article_data

    def _insert_images(self, content: str, images: List[ImageInfo], affiliate_url: str) -> str:
        """Insert images into article content

        Args:
            content: Article content
            images: List of images to insert

        Returns:
            str: Updated article content with images
        """
        print("_insert_images images: ", images)
        if not images:
            return content

        paragraphs = content.split("\n\n")
        button_positions = set()
        used_positions = set()

        # 找出所有按钮位置
        for i, p in enumerate(paragraphs):
            if '<div style="text-align: center; margin: 20px 0' in p:
                button_positions.add(i)

        # 找出所有可以插入图片的位置
        valid_positions = []
        for i, p in enumerate(paragraphs):
            # 跳过最后一个段落避免影响结尾
            if i == len(paragraphs) - 1:
                continue
            if not p.startswith("#") and \
                    i not in button_positions and \
                    i - 1 not in button_positions and \
                    i + 1 not in button_positions:
                valid_positions.append(i)

        # 如果没有合适位置但有图片，在末尾插入一张
        if not valid_positions and len(paragraphs) > 0 and images:
            paragraphs[-1] += f"\n\n[![{images[0].title}]({images[0].url})]({affiliate_url})"
            return "\n\n".join(paragraphs)

        # 确定要使用的图片数量
        # 目标：在有足够空间时插入2-4张图片，但不超过可用位置的2/3
        available_slots = len(valid_positions)
        if available_slots <= 1:
            num_images = 1 if images else 0
        else:
            # 至少2张，最多4张，或可用位置的2/3，取最小值
            max_images = min(4, max(2, available_slots * 2 // 3))
            num_images = min(len(images), max_images)

        # 计算步长，保证均匀分布
        step = max(1, available_slots // num_images) if num_images > 0 else 1

        # 选择插入位置
        insert_positions = []
        for i in range(num_images):
            pos_index = i * step
            if pos_index < len(valid_positions) and valid_positions[pos_index] not in used_positions:
                insert_positions.append(valid_positions[pos_index])
                used_positions.add(valid_positions[pos_index])

        # 如果计算出的位置不足目标数量，尝试补充
        remaining = num_images - len(insert_positions)
        if remaining > 0:
            for i in range(len(valid_positions)):
                if valid_positions[i] not in used_positions and remaining > 0:
                    insert_positions.append(valid_positions[i])
                    used_positions.add(valid_positions[i])
                    remaining -= 1

        # 插入图片
        for pos, img in zip(sorted(insert_positions), images[:len(insert_positions)]):
            paragraphs[pos] += f"\n\n[![{img.title}]({img.url})]({affiliate_url})"

        # 如果没有插入任何图片但有可用图片，确保至少插入一张
        if not insert_positions and images and len(paragraphs) > 0:
            paragraphs[-1] += f"\n\n[![{images[0].title}]({images[0].url})]({affiliate_url})"

        return "\n\n".join(paragraphs)

    def _insert_buttons(self, content: str, buttons: List[str]) -> str:
        paragraphs = content.split("\n\n")
        num_paragraphs = len(paragraphs)

        # 确保至少有3个段落才开始插入按钮
        if num_paragraphs < 3:
            return content

        if num_paragraphs >= 5:
            # 计算步长，确保按钮之间至少间隔一个段落
            step = max((num_paragraphs - 2) // len(buttons), 2)
            for i, button in enumerate(buttons):
                # 从第三个段落开始插入，并保持间隔
                insert_pos = 2 + (i * step)
                if insert_pos < num_paragraphs:
                    paragraphs[insert_pos] += f"\n\n{button}"
        else:
            # 对于较短的文章，从第三个段落开始，每隔一个段落插入一个按钮
            step = 2
            for i, button in enumerate(buttons):
                insert_pos = min(2 + (i * step), num_paragraphs - 1)
                if insert_pos < num_paragraphs:
                    paragraphs[insert_pos] += f"\n\n{button}"
        return "\n\n".join(paragraphs)

    def _insert_reviews(self, reviews: List[Review]) -> str:
        """Format and insert reviews section"""
        md = ""

        # Add reviews with random title
        review_titles = [
            "Real User Feedback",
            "User Experience",
            "Featured Customer Reviews",
            "User Testimonials",
            "Authentic User Reviews"
        ]

        if reviews:
            import random
            review_title = random.choice(review_titles)
            md += f"## {review_title}\n\n"

            for r in reviews:
                md += f"### {r.author} ({r.platform})\n"
                md += f"Rating: {r.rating}/5 | Date: {r.date.strftime('%Y-%m-%d') if r.date else 'N/A'}\n\n"
                md += f"{r.content}\n"
                md += f"[View Original Review]({r.url})\n\n"

        return md

    def _insert_references(self, references: List[SearchResult]) -> str:
        """Format and insert reference articles section"""
        if not references:
            return ""

        reference_titles = [
            "Further Reading",
            "Related Content",
            "More Information",
            "Recommended Reading",
            "Popular Articles"
        ]

        import random
        reference_title = random.choice(reference_titles)
        md = f"## {reference_title}\n\n"

        for i, ref in enumerate(references, 1):
            md += f"{i}. [{ref.title}]({ref.link})\n"
            md += f"   {ref.snippet}\n\n"

        return md


def main():
    """Usage example"""
    # 1. 初始化整合器
    integrator = Integrator()

    # 2. 生成文章
    site = "heima24.de"  # Replace with target website domain
    proxy_info = "socks5://Idv8YNmuqPmkqWx:DhSD35p2yyms6Cz@*************:41551"  # Set proxy information if needed
    article_data = integrator.generate_article(site, proxy_info)

    # 3. Output as JSON format
    json_content = article_data.to_json()
    print("\nJSON format output:")
    print(json_content)


if __name__ == "__main__":
    main()
