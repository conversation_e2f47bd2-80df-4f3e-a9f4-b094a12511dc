import json
import re
import g4f
from internal.generator import prompt

gpt_model_list = ["gpt_4_5", "gemini_2_5_pro", "grok_3", "gpt_4_1", "grok_3_r1"]


def generate_content(prompt_text, gpt_model_name, proxy_info):
    g4f.debug.version_check = False
    gpt_model_name = gpt_model_name.strip()
    function_gpt_model = gpt_model_name
    # 构造语句
    statement = f"gpt_model = g4f.models.{function_gpt_model}"
    # 执行语句
    gpt_model = g4f.models.default
    try:
        exec(statement)
    except AttributeError:
        print("无效的 gpt model:", gpt_model_name)
        return ""
    ret = g4f.ChatCompletion.create(
        model=gpt_model,
        messages=[{"role": "user", "content": prompt_text}],
        proxy=proxy_info,
    )  # alternative model setting
    return ret


def generate_article_content(site: str, proxy_info: str = None):
    prompt_text = prompt.generate_prompt(site)
    for gpt_model_name in gpt_model_list:
        ret = generate_content(prompt_text, gpt_model_name, proxy_info)
        match = re.search(r'\{[^{}]*\}', ret)
        if match:
            json_str = match.group()
            try:
                data = json.loads(json_str)
                return data
            except:
                try:
                    def fix_multiline(match):
                        key = match.group(1)
                        value = match.group(2).replace('\n', '\\n').replace('"', '\\"').strip()
                        return f'"{key}": "{value}"'

                    # 处理键值对中的多行字符串
                    json_str_fixed = re.sub(r'"([^"]+)":\s*"(.*?)"(?=\s*[,}])', fix_multiline, json_str,
                                            flags=re.DOTALL)
                    data = json.loads(json_str_fixed)
                    return data
                except:
                    continue
        else:
            # 解析失败，标题和描述留空
            print("解析失败！")
            continue
    return ""
