from urllib.parse import quote
from internal.utils import domainutil
from typing import Dict, List, Optional
import urllib.parse
import requests
import json
from bs4 import BeautifulSoup
from dataclasses import dataclass
from duckduckgo_search import DDGS


@dataclass
class ImageInfo:
    """图片信息数据类"""
    url: str  # 图片URL
    title: str  # 图片标题
    thumbnail: str  # 缩略图URL
    source: str  # 图片来源URL
    width: int  # 图片宽度
    height: int  # 图片高度
    image_type: str  # 图片类型(jpg/png等)
    source_name: str  # 来源网站名称
    search_engine: str  # 搜索引擎(bing/duckduckgo)
    is_featured: bool = False  # 是否为特色图片
    score: float = 0.0  # 图片评分


headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
}


def get_website_logo(site: str) -> str:
    """
    获取网站logo
    :param site: 网站域名，如 example.com
    :return: logo的URL
    """
    domain = domainutil.extract_domain(site)
    logo_url = f"https://logo.clearbit.com/{domain}"
    return logo_url


def get_website_screenshot(site: str) -> str:
    """
    获取网站首页截图
    :param site: 网站URL，如 example.com
    :return: 截图的URL
    """
    domain = domainutil.extract_domain(site)
    screenshot_url = f"https://s.wordpress.com/mshots/v1/{quote(domain)}"
    return screenshot_url


def calculate_bing_image_score(image: Dict) -> float:
    """计算Bing图片得分（不考虑尺寸）"""
    score = 0.0

    # 1. 标题相关性得分 (0-40分)
    title = image.get('title', '').lower()
    relevant_keywords = ['official', 'screenshot', 'product', 'feature', 'interface']
    for keyword in relevant_keywords:
        if keyword in title:
            score += 8  # 每个相关关键词加8分

    # 2. 来源可信度得分 (0-30分)
    source = image.get('source', '').lower()
    if 'official' in source:
        score += 30
    elif any(domain in source for domain in ['.com', '.org', '.net']):
        score += 20
    else:
        score += 10

    # 3. 图片格式得分 (0-20分)
    url = image.get('url', '').lower()
    if url.endswith(('.jpg', '.jpeg', '.png')):
        score += 20
    elif url.endswith('.webp'):
        score += 15
    elif url.endswith('.gif'):
        score += 10

    # 4. 缩略图可用性得分 (0-10分)
    if image.get('thumbnail'):
        score += 10
    return score


def calculate_duckduckgo_image_score(image: Dict) -> float:
    """计算DuckDuckGo图片得分（包含尺寸要求）"""
    score = 0.0

    # 1. 分辨率得分 (0-40分)
    width = image['width']
    height = image['height']
    if width >= 1920 and height >= 1080:
        score += 40
    elif width >= 1280 and height >= 720:
        score += 30
    elif width >= 800 and height >= 600:
        score += 20
    elif width >= 400 and height >= 300:
        score += 10
    else:
        return 0  # 不满足最低尺寸要求

    # 2. 图片比例得分 (0-30分)
    ratio = width / height if height else 0
    if 1.3 <= ratio <= 1.8:
        score += 30
    elif 1.0 <= ratio <= 2.0:
        score += 20
    elif 0.5 <= ratio <= 2.5:
        score += 10

    # 3. 图片类型得分 (0-20分)
    image_type = image.get('image_type', '').lower()
    if image_type in ['jpg', 'jpeg', 'png']:
        score += 20
    elif image_type in ['webp']:
        score += 15

    # 4. 来源可信度得分 (0-10分)
    source_name = image.get('source_name', '').lower()
    trusted_sources = ['official', 'press', 'news', 'blog']
    if any(source in source_name for source in trusted_sources):
        score += 10

    return score


def is_valid_image_url(url: str) -> tuple[bool, str]:
    """验证是否是合法的图片URL
    
    Returns:
        tuple: (是否为合法图片URL, 图片类型)
    """
    try:
        # 去除URL中的参数部分
        url_without_params = url.split('?')[0].lower()

        # 检查文件扩展名
        image_extensions = ('.jpg', '.jpeg', '.png', '.webp')
        for ext in image_extensions:
            if url_without_params.endswith(ext):
                # 对于jpeg和jpg返回统一的类型
                image_type = 'jpg' if ext in ('.jpg', '.jpeg') else ext.lstrip('.')
                return True, image_type
        return False, ''

    except Exception as e:
        return False, ''


def make_request(url: str, current_proxy: Dict or None) -> Optional[requests.Response]:
    """发送HTTP请求，支持代理"""
    # 如果有可用的代理配置，优先使用代理
    if current_proxy:
        try:
            response = requests.get(
                url,
                headers=headers,
                proxies=current_proxy,
                timeout=(5, 10),
            )

            if response.status_code == 200:
                return response
        except Exception as e:
            pass

    # 如果没有代理或所有代理都失败，尝试直接请求
    try:
        response = requests.get(
            url,
            headers=headers,
            timeout=(5, 10),
        )
        if response.status_code == 200:
            return response
    except Exception as e:
        pass

    return None


def search_images_by_duckduckgo(site: str, current_proxy: str or None) -> List[Dict]:
    """通过DuckDuckGo搜索图片"""
    image_list = []
    try:
        # 创建 DDG 实例
        ddg = DDGS(proxy=current_proxy)
        # 搜索图片
        results = ddg.images(
            keywords=site,
            region="wt-wt",  # 全球范围
            safesearch="off",  # 可根据需要调整
            max_results=30
        )

        # 转换为目标格式
        for item in results:
            image_info = {
                'url': item.get('image', ''),
                'title': item.get('title', ''),
                'thumbnail': item.get('thumbnail', ''),
                'source': item.get('url', ''),
                'width': item.get('width', 0),
                'height': item.get('height', 0),
                'provider': "",
                'image_type': "",  # duckduckgo_search 未提供此字段
                'source_name': item.get('source', ''),
                'is_featured': False,
                'search_engine': 'duckduckgo',
                'score': 0.0,
            }
            # 验证图片基本信息
            flag_valid_image, image_type = is_valid_image_url(image_info['url'])
            if not flag_valid_image:
                continue
            # 验证图片尺寸
            if image_info['width'] < 400 or image_info['height'] < 300:
                continue
            image_list.append(image_info)
    except Exception as e:
        print(f"搜索图片时出错: {str(e)}")
    return image_list


def search_images_by_bing(keyword: str, current_proxy: Dict or None) -> List[Dict]:
    """通过Bing搜索图片"""
    try:
        images = []
        current = 0
        while current < 150 and len(images) < 15:  # 获取更多候选图片
            request_url = (
                    'https://www.bing.com/images/async?q=' +
                    urllib.parse.quote_plus(keyword) +
                    '&first=' + str(current) +
                    '&count=40'
            )
            response = make_request(request_url, current_proxy)
            if not response:
                break

            soup = BeautifulSoup(response.text, 'html.parser')
            image_elements = soup.find_all('a', class_='iusc')
            if not image_elements:
                break

            for element in image_elements:
                try:
                    m = json.loads(element.get('m', '{}'))
                    image_info = {
                        'url': m.get('murl', ''),
                        'title': m.get('t', ''),
                        'thumbnail': m.get('turl', ''),
                        'source': m.get('purl', ''),
                        'width': m.get('w', 0),
                        'height': m.get('h', 0),
                        'provider': m.get('prov', []),
                        'search_engine': 'bing',
                        "is_featured": False,
                        'source_name': '',
                        'score': 0.0,
                    }
                    flag_valid_image, image_type = is_valid_image_url(image_info['url'])
                    if flag_valid_image:
                        image_info["image_type"] = image_type
                        images.append(image_info)
                except Exception as e:
                    continue
            current += 40
        return images
    except Exception as e:
        return []


def search_images(site: str, proxy: str) -> List[ImageInfo]:
    """
    搜索图片，优先使用DuckDuckGo（三次尝试），失败后才使用Bing
    """
    images = []
    current_proxy = None
    # 第一次尝试：使用DuckDuckGo 搜索
    try:
        # 如果有代理就使用代理
        images.extend(search_images_by_duckduckgo(site, proxy))
    except Exception as e:
        pass
    # 第二次尝试：Bing 搜索
    if len(images) <= 0:
        try:
            if proxy:
                proxy_config = {
                    'http': proxy,
                    'https': proxy
                }
                current_proxy = proxy_config
            images.extend(search_images_by_bing(site, current_proxy))
        except Exception as e:
            pass
    # 第三次尝试：DuckDuckGo 搜索（强制不使用代理）
    if len(images) <= 0:
        try:
            current_proxy = None
            images.extend(search_images_by_duckduckgo(site, current_proxy))
        except Exception as e:
            pass
    # 第四次尝试：Bing 搜索（强制不使用代理）
    if len(images) <= 0:
        try:
            current_proxy = None
            images.extend(search_images_by_bing(site, current_proxy))
        except Exception as e:
            pass
    if len(images) <= 0:
        return []
    # 为所有图片计算得分
    scored_images = []
    for image in images:
        try:
            # 根据搜索引擎选择不同的评分方法
            score = 0.0
            if image.get('search_engine') == 'bing':
                score = calculate_bing_image_score(image)
            elif image.get('search_engine') == 'duckduckgo':
                score = calculate_duckduckgo_image_score(image)
            image['score'] = score
            scored_images.append((score, image))
        except Exception as e:
            continue
    if len(scored_images) <= 0:
        return []
    # 按得分排序
    scored_images.sort(key=lambda x: x[0], reverse=True)

    # 获取最终的图片列表（取前10张）
    result_images = []
    for score, img in scored_images:
        try:
            # 确保所有必要字段都存在且类型正确
            image_data = ImageInfo(
                url=str(img.get('url', '')),
                title=str(img.get('title', '')),
                thumbnail=str(img.get('thumbnail', '')),
                source=str(img.get('source', '')),
                width=int(img.get('width', 0)),
                height=int(img.get('height', 0)),
                image_type=str(img.get('image_type', '')),
                source_name=str(img.get('source_name', '')),
                search_engine=str(img.get('search_engine', '')),
                is_featured=bool(img.get('is_featured', False)),
                score=float(score),
            )
            result_images.append(image_data)
        except (ValueError, TypeError, KeyError) as e:
            print(f"跳过无效的图片数据: {str(e)}")
            continue

    # 标记特色图片（得分最高的）
    for i, image in enumerate(result_images):
        image.is_featured = (i == 0)
    return result_images


if __name__ == "__main__":
    images = search_images("aiper.com", "socks5://Idv8YNmuqPmkqWx:DhSD35p2yyms6Cz@*************:41551")
    print(images)
