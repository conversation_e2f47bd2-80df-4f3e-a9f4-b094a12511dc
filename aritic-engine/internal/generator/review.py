import json
import time
from datetime import datetime
from typing import List, Optional
from urllib.parse import quote
import requests
from bs4 import BeautifulSoup
from dataclasses import dataclass

from internal.utils import domainutil


@dataclass
class Review:
    platform: str
    rating: float
    content: str
    author: str
    date: datetime
    url: str


def get_trustpilot_reviews(domain: str) -> tuple[List[Review], Optional[str]]:
    """获取指定域名在Trustpilot上的最近10个4.5星以上的好评

    Args:
        domain: 网站域名

    Returns:
        tuple[List[Review], Optional[str]]: (评论列表, 错误信息)
    """
    print(f"开始处理域名: {domain}")

    # 格式化域名

    domain = domainutil.extract_domain(domain)
    print(f"格式化后的域名: {domain}")

    # 构建Trustpilot URL
    base_url = f"https://www.trustpilot.com/review/{quote(domain)}"
    print(f"目标URL: {base_url}")

    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    }

    try:
        # 发送请求获取页面内容
        response = requests.get(base_url, headers=headers, timeout=30)
        response.raise_for_status()

        # 使用BeautifulSoup解析页面
        soup = BeautifulSoup(response.text, 'html.parser')

        # 查找包含评论数据的script标签
        next_data = soup.find('script', {'id': '__NEXT_DATA__'})
        if not next_data:
            return [], "无法找到评论数据"

        # 解析JSON数据
        data = json.loads(next_data.string)
        reviews_data = data.get('props', {}).get('pageProps', {}).get('reviews', [])

        reviews = []
        for review_data in reviews_data:
            # 如果已经收集了10个评论，则跳过
            if len(reviews) >= 10:
                print("已达到10条评论上限，跳过剩余评论")
                break

            # 只处理4.5星以上的评论
            rating = review_data.get('rating', 0)
            if rating < 4.5:
                print("评分低于4.5星，跳过该评论")
                continue

            try:
                # 解析日期
                date_str = review_data.get('dates', {}).get('publishedDate')
                date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))

                # 创建Review对象
                review = Review(
                    platform="Trustpilot",
                    rating=rating,
                    content=review_data.get('text', ''),
                    author=review_data.get('consumer', {}).get('displayName', ''),
                    date=date,
                    url=f"https://www.trustpilot.com{review_data.get('permalink', '')}"
                )
                reviews.append(review)

            except Exception as e:
                print(f"处理评论时出错: {str(e)}")
                continue

        return reviews, None

    except requests.RequestException as e:
        return [], f"请求失败: {str(e)}"
    except json.JSONDecodeError as e:
        return [], f"JSON解析失败: {str(e)}"
    except Exception as e:
        return [], f"未知错误: {str(e)}"


if __name__ == "__main__":
    li = get_trustpilot_reviews("heima24.de")
    print(li)
