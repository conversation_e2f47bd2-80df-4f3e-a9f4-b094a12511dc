# integrator.py
import json
from typing import List, Optional, Tuple
from datetime import datetime
import random
import time

# 假设这些是你的模块导入
import ai
import image
import marketing
import reference
import review


class AffiliateContentGenerationError(Exception):
    """自定义异常，用于文章内容生成失败"""
    pass


class AffiliateContentGenerator:
    def __init__(self, site: str, affiliate_url: str, proxy_info: str = None, max_retries: int = 3):
        self.site = site
        self.affiliate_url = affiliate_url
        self.proxy_info = proxy_info
        self.domain = self._extract_domain(site)
        self.max_retries = max_retries
        self.review_titles = [
            "## Customer Reviews and Feedback",
            "## What Users Are Saying",
            "## Real User Experiences",
            "## Community Ratings"
        ]
        self.reference_titles = [
            "## Related Resources",
            "## Additional Reading",
            "## Useful References",
            "## Explore More"
        ]

    def _extract_domain(self, site: str) -> str:
        return site.replace("https://", "").replace("http://", "").split("/")[0]

    def _insert_buttons(self, content: str, buttons: List[str]) -> str:
        paragraphs = content.split("\n\n")
        num_paragraphs = len(paragraphs)

        if num_paragraphs >= 5:
            step = num_paragraphs // 5
            for i, button in enumerate(buttons):
                insert_pos = (i + 1) * step - 1
                if insert_pos < num_paragraphs:
                    paragraphs[insert_pos] += f"\n\n{button}"
        else:
            step = max(1, num_paragraphs // len(buttons))
            for i, button in enumerate(buttons):
                insert_pos = min(i * step, num_paragraphs - 1)
                paragraphs[insert_pos] += f"\n\n{button}"

        return "\n\n".join(paragraphs)

    def _append_reviews(self, content: str, reviews: List[review.Review]) -> str:
        if not reviews:
            return content

        random_title = random.choice(self.review_titles)
        reviews_section = [random_title]

        for r in reviews[:5]:
            review_text = f"### {r.rating}/5 - {r.author}\n" \
                          f"*Posted on {r.date.strftime('%Y-%m-%d')} via {r.platform}*\n\n" \
                          f"{r.content}\n\n" \
                          f"[Read full review]({r.url})"
            reviews_section.append(review_text)

        return f"{content}\n\n" + "\n\n".join(reviews_section)

    def _append_references(self, content: str, references: List[reference.SearchResult]) -> str:
        if not references:
            return content

        random_title = random.choice(self.reference_titles)
        ref_section = [random_title]

        for ref in references[:5]:
            ref_text = f"- [{ref.title}]({ref.link})\n" \
                       f"  {ref.snippet}"
            ref_section.append(ref_text)

        return f"{content}\n\n" + "\n\n".join(ref_section)

    def _fetch_article_content_with_retry(self) -> dict:
        """带重试机制的文章内容获取，失败则抛出异常"""
        attempts = 0
        while attempts < self.max_retries:
            article_data = None  # 初始化变量
            try:
                article_data = ai.generate_article_content(self.site, self.proxy_info)
                print(f"Attempt {attempts + 1}/{self.max_retries}: Raw result = {article_data!r}")
                print(f"Attempt {attempts + 1}/{self.max_retries}: Type = {type(article_data)}")

                # 检查是否为空
                if not article_data:
                    print(f"Attempt {attempts + 1}/{self.max_retries}: Empty result")
                    attempts += 1
                    time.sleep(1)
                    continue

                # 处理返回值类型
                if isinstance(article_data, str):
                    try:
                        article_data = json.loads(article_data)
                        print(f"Attempt {attempts + 1}/{self.max_retries}: Successfully parsed JSON")
                    except json.JSONDecodeError as e:
                        print(f"Attempt {attempts + 1}/{self.max_retries}: JSON decode error - {e}")
                        print(f"Attempt {attempts + 1}/{self.max_retries}: Invalid JSON content = {article_data!r}")
                        attempts += 1
                        time.sleep(1)
                        continue
                elif not isinstance(article_data, dict):
                    print(f"Attempt {attempts + 1}/{self.max_retries}: Unexpected type {type(article_data)}")
                    attempts += 1
                    time.sleep(1)
                    continue

                return article_data

            except Exception as e:
                print(f"Attempt {attempts + 1}/{self.max_retries}: Unexpected error - {str(e)}")
                print(f"Attempt {attempts + 1}/{self.max_retries}: Raw result before error = {article_data!r}")
                attempts += 1
                time.sleep(1)

        raise AffiliateContentGenerationError(
            f"Failed to fetch valid article content after {self.max_retries} attempts"
        )

    def generate_complete_content(self) -> dict:
        """生成完整的affiliate内容"""
        # 1. 生成文章内容（带重试）
        article_data = self._fetch_article_content_with_retry()

        # 2. 获取网站logo
        logo_url = image.get_website_logo(self.site)

        # 3. 获取网站截图
        screenshot_url = image.get_website_screenshot(self.site)

        # 4. 生成5个按钮
        buttons = [marketing.generate_button(self.affiliate_url, self.domain)
                   for _ in range(5)]

        # 5. 获取参考文章
        config = reference.ReferenceConfig()
        ref = reference.Reference(config)
        reference_articles = ref.search_web(self.domain)

        # 6. 获取评论
        reviews, review_error = review.get_trustpilot_reviews(self.domain)

        # 处理文章内容
        modified_content = self._insert_buttons(article_data["content"], buttons)
        modified_content = self._append_reviews(modified_content, reviews)
        modified_content = self._append_references(modified_content, reference_articles)

        article_data["content"] = modified_content

        result = {
            "article": article_data,
            "media": {
                "logo_url": logo_url,
                "screenshot_url": screenshot_url,
                "suggested_image": article_data["image_suggestion"]
            },
            "marketing": {
                "buttons": buttons
            },
            "references": [
                {
                    "title": ref.title,
                    "link": ref.link,
                    "snippet": ref.snippet
                } for ref in reference_articles
            ],
            "reviews": [
                {
                    "platform": r.platform,
                    "rating": r.rating,
                    "content": r.content,
                    "author": r.author,
                    "date": r.date.isoformat(),
                    "url": r.url
                } for r in reviews
            ] if reviews else [],
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "site": self.site,
                "affiliate_url": self.affiliate_url
            }
        }

        if review_error:
            result["review_error"] = review_error

        return result

    def save_to_file(self, output_path: str) -> None:
        content = self.generate_complete_content()
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(content, f, ensure_ascii=False, indent=2)


def main():
    try:
        generator = AffiliateContentGenerator(
            site="https://heima24.de",
            affiliate_url="https://heima24.de/affiliate-link",
            max_retries=3
        )
        result = generator.generate_complete_content()
        generator.save_to_file("affiliate_content.json")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except AffiliateContentGenerationError as e:
        print(f"Error: {e}")
        raise


if __name__ == "__main__":
    main()