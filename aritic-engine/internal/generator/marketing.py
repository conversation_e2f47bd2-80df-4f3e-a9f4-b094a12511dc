import random
import time

button_templates = [
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #00b67a, #008c5e); color: white; text-decoration: none; border-radius: 6px; font-weight: 600; transition: transform 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0, 0, 0, 0.2)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0, 0, 0, 0.1)\';" target="_blank" rel="noopener noreferrer">Shop Now on {domain} →</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 10px 20px; background: linear-gradient(135deg, #f39c12, #e67e22); color: white; text-decoration: none; border-radius: 5px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Discover Amazing Offers on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Unlock Exclusive Deals at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #3498db, #2980b9); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Shop the Latest Trends on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #1abc9c, #16a085); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Find Your Perfect Match at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Get the Best Deals Now on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #2ecc71, #27ae60); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Limited Time Offer at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #34495e, #2c3e50); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Explore New Arrivals on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #f1c40f, #f39c12); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Don\'t Miss Out on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #e67e22, #d35400); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Shop Exclusive Offers at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Discover the Best at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #2980b9, #3498db); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Your Next Purchase Awaits at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #16a085, #1abc9c); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Exclusive Savings at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #c0392b, #e74c3c); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Act Fast! Deals on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Shop Now & Save at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #2c3e50, #34495e); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Uncover Great Deals at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #f39c12, #f1c40f); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Best Offers Await at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #d35400, #e67e22); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Limited Time Savings at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #8e44ad, #9b59b6); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Exclusive Deals Just for You at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #3498db, #2980b9); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Find Your Next Favorite on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #16a085, #1abc9c); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; transition: transform 0.2s;" onmouseover="this.style.transform=\'translateY(-3px)\';" onmouseout="this.style.transform=\'translateY(0)\';" target="_blank" rel="noopener noreferrer">Discover More at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #e91e63, #c2185b); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #d81b60, #ad1457)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #e91e63, #c2185b)\';" target="_blank" rel="noopener noreferrer">Grab Hot Deals Now on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #ff9800, #f57c00); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #fb8c00, #ef6c00)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #ff9800, #f57c00)\';" target="_blank" rel="noopener noreferrer">Unlock Big Savings at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #4caf50, #388e3c); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #43a047, #2e7d32)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #4caf50, #388e3c)\';" target="_blank" rel="noopener noreferrer">Shop Top Picks on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #ff5722, #e64a19); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #f4511e, #d84315)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #ff5722, #e64a19)\';" target="_blank" rel="noopener noreferrer">Snag Exclusive Offers at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #03a9f4, #0288d1); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #039be5, #0277bd)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #03a9f4, #0288d1)\';" target="_blank" rel="noopener noreferrer">Dive into Deals on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #ffeb3b, #fbc02d); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #fdd835, #ffb300)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #ffeb3b, #fbc02d)\';" target="_blank" rel="noopener noreferrer">Brighten Your Day with {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #673ab7, #5e35b1); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #5e35b1, #512da8)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #673ab7, #5e35b1)\';" target="_blank" rel="noopener noreferrer">Score Big at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #009688, #00796b); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #00897b, #00695c)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #009688, #00796b)\';" target="_blank" rel="noopener noreferrer">Shop Smart on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #ffca28, #ffb300); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #ffb300, #ffa000)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #ffca28, #ffb300)\';" target="_blank" rel="noopener noreferrer">Golden Deals Await at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #795548, #6d4c41); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #6d4c41, #5d4037)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #795548, #6d4c41)\';" target="_blank" rel="noopener noreferrer">Steal These Offers on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #2196f3, #1976d2); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #1e88e5, #1565c0)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #2196f3, #1976d2)\';" target="_blank" rel="noopener noreferrer">Jump on These Savings at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #8bc34a, #7cb342); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #7cb342, #689f38)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #8bc34a, #7cb342)\';" target="_blank" rel="noopener noreferrer">Fresh Deals Now on {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #f06292, #ec407a); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #ec407a, #d81b60)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #f06292, #ec407a)\';" target="_blank" rel="noopener noreferrer">Love These Offers at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #00bcd4, #00acc1); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #00acc1, #0097a7)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #00bcd4, #00acc1)\';" target="_blank" rel="noopener noreferrer">Cool Savings Await at {domain}</a></div>',
    '<div style="text-align: center; margin: 20px 0;"><a href="{affiliate_url}" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #607d8b, #546e7a); color: white; text-decoration: none; border-radius: 6px; font-weight: bold; transition: transform 0.2s, box-shadow 0.2s, background 0.2s; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 8px rgba(0,0,0,0.2)\'; this.style.background=\'linear-gradient(135deg, #546e7a, #455a64)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 4px rgba(0,0,0,0.1)\'; this.style.background=\'linear-gradient(135deg, #607d8b, #546e7a)\';" target="_blank" rel="noopener noreferrer">Unlock Hidden Gems on {domain}</a></div>',
]


def generate_button(affiliate_url: str, domain: str) -> str:
    # 设置随机种子，使用当前时间
    random.seed(time.time_ns())
    # 随机选择一个模板
    button_html = random.choice(button_templates)
    # 替换占位符
    button_html = button_html.replace("{affiliate_url}", affiliate_url)
    button_html = button_html.replace("{domain}", domain)
    return button_html


def generate_buttons(affiliate_url: str, domain: str, count: int = 5) -> list[str]:
    """
    随机生成指定数量的按钮并返回列表。

    Args:
        affiliate_url (str): Affiliate 链接
        domain (str): 域名
        count (int): 需要生成的按钮数量

    Returns:
        list[str]: 包含指定数量按钮 HTML 的列表
    """
    if count < 0:
        raise ValueError("Button count must be non-negative")

    # 生成指定数量的按钮
    buttons = []
    for _ in range(count):
        button_html = generate_button(affiliate_url, domain)
        buttons.append(button_html)

    return buttons


# 示例使用
if __name__ == "__main__":
    affiliate_url = "https://bilibili.com/affiliate"
    domain = "ExampleShop"
    button = generate_button(affiliate_url, domain)
    print(button)
