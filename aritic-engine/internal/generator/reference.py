import random
import time
from typing import List, Dict, Optional
from urllib.parse import urlencode, parse_qs, urlparse
import scrapy
from scrapy.crawler import CrawlerProcess
from scrapy.http import Request, Response

DUCKDUCKGO_SEARCH_URL = "https://html.duckduckgo.com/html"

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
]

SKIP_DOMAINS = ["wikipedia.org", "facebook.com"]
REVIEW_KEYWORDS = ["review", "guide"]


class SearchResult:
    def __init__(self, title: str, link: str, snippet: str, content: str = ""):
        self.title = title
        self.link = link
        self.snippet = snippet
        self.content = content


class ReferenceConfig:
    def __init__(self, max_results: int = 10, max_retries: int = 3, timeout: float = 30.0,
                 concurrency: int = 2, delay_min: int = 2000, delay_max: int = 5000,
                 use_random_agent: bool = True, proxy_url: str = "", use_proxy: bool = False):
        self.max_results = max_results
        self.max_retries = max_retries
        self.timeout = timeout
        self.concurrency = concurrency
        self.delay_min = delay_min
        self.delay_max = delay_max
        self.use_random_agent = use_random_agent
        self.proxy_url = proxy_url
        self.use_proxy = use_proxy


class DuckDuckGoSpider(scrapy.Spider):
    name = "duckduckgo"

    def __init__(self, reference, keyword, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.reference = reference
        self.keyword = keyword

    def start_requests(self):
        queries = self.reference.get_random_keyword_combinations(self.keyword, 3)
        for query in queries:
            params = {'q': query, 'kl': 'us-en', 's': '0'}
            url = f"{DUCKDUCKGO_SEARCH_URL}?{urlencode(params)}"
            yield Request(
                url=url,
                callback=self.parse,
                headers=self.reference.get_request_headers(),
                meta={'proxy': self.reference.config.proxy_url} if self.reference.config.use_proxy else {}
            )

    def parse(self, response: Response):
        for result in response.css('.result'):
            search_result = self.reference.process_duckduckgo_search_result(result)
            if search_result and search_result.link not in self.reference.seen_urls:
                self.reference.seen_urls[search_result.link] = True
                self.reference.results.append(search_result)
                if len(self.reference.results) >= self.reference.config.max_results:
                    return


class Reference:
    def __init__(self, config: ReferenceConfig):
        self.config = config
        self.results = []
        self.seen_urls = {}

    def get_random_user_agent(self) -> str:
        return random.choice(USER_AGENTS)

    def random_delay(self, min_delay: int, max_delay: int):
        delay = random.randint(min_delay, max_delay) / 1000
        time.sleep(delay)

    def should_skip_domain(self, link: str) -> bool:
        link_lower = link.lower()
        return any(domain in link_lower for domain in SKIP_DOMAINS)

    def get_random_keyword_combinations(self, keyword: str, count: int) -> List[str]:
        keywords_copy = REVIEW_KEYWORDS.copy()
        selected_keywords = random.sample(keywords_copy, min(count, len(keywords_copy)))
        return [f"{keyword} {kw}" for kw in selected_keywords]

    def get_request_headers(self) -> Dict[str, str]:
        headers = {'Accept': 'text/html,application/xhtml+xml'}
        if self.config.use_random_agent:
            headers['User-Agent'] = self.get_random_user_agent()
        else:
            headers['User-Agent'] = USER_AGENTS[0]
        return headers

    def search_web(self, keyword: str) -> List[SearchResult]:
        from internal.utils import portutil
        self.results = []
        self.seen_urls = {}

        # 查找可用端口
        telnet_port = portutil.find_free_port(6000, 7000)
        if not telnet_port:
            # 如果没有可用端口，尝试终止占用端口的进程
            default_port = 6023
            if portutil.kill_process_by_port(default_port):
                telnet_port = default_port
            else:
                raise RuntimeError("无法找到可用端口")

        settings = {
            'DOWNLOAD_DELAY': random.randint(self.config.delay_min, self.config.delay_max) / 1000,
            'CONCURRENT_REQUESTS': self.config.concurrency,
            'DOWNLOAD_TIMEOUT': self.config.timeout,
            'LOG_LEVEL': 'ERROR',  # 只记录错误日志
            'RETRY_TIMES': self.config.max_retries,
            'TELNETCONSOLE_PORT': [telnet_port],  # 设置Telnet控制台端口为列表格式
        }
        if self.config.use_proxy and self.config.proxy_url:
            settings['PROXY'] = self.config.proxy_url

        process = CrawlerProcess(settings)
        process.crawl(DuckDuckGoSpider, reference=self, keyword=keyword)
        process.start()

        if not self.results:
            for retry in range(self.config.max_retries):
                self.random_delay(self.config.delay_min * 2, self.config.delay_max * 2)
                self.results = []
                self.seen_urls = {}
                process = CrawlerProcess(settings)
                process.crawl(DuckDuckGoSpider, reference=self, keyword=keyword)
                process.start()
                if self.results:
                    break

        return self.results[:self.config.max_results]

    def process_duckduckgo_search_result(self, result) -> Optional[SearchResult]:
        title = result.css('.result__a::text').get() or result.css('.result__title::text').get()
        if not title:
            return None

        link = result.css('.result__a::attr(href)').get()
        if link and link.startswith("/d.js?"):
            try:
                parsed_url = urlparse(link)
                params = parse_qs(parsed_url.query)
                if 'uddg' in params and params['uddg']:
                    link = params['uddg'][0]
            except Exception:
                pass

        if not link or self.should_skip_domain(link):
            return None

        snippet = result.css('.result__snippet::text').get(default='')
        return SearchResult(title=title.strip(), link=link, snippet=snippet.strip())


if __name__ == "__main__":
    config = ReferenceConfig()
    ref = Reference(config)
    results = ref.search_web("heima24.de")
    print("results: ", len(results))
    for r in results:
        print(f"Title: {r.title}\nLink: {r.link}\nSnippet: {r.snippet}\n---")
