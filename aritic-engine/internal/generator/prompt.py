ARTICLE_PROMPT = '''Generate a comprehensive soft article of [2000-2500 words] recommending the product or service from {site}. Begin by visiting {site} to gather essential information (e.g., product/service details, target audience, primary long-tail keyword, two secondary keywords, brand tone/values, slogan if available). Craft an engaging, value-driven piece that sparks curiosity, subtly highlights the offering’s benefits, and encourages readers to click the website link for purchase, while ensuring it’s SEO-optimized for Google indexing.

Article Language: English

Detailed Requirements:

1. Information Gathering:
   - Visit {site} using web browsing capabilities to identify:
     - Product or service description (from homepage, product pages, about page, etc.).
     - Target audience (infer from content if not explicit, e.g., busy parents from a time-saving tool site, or assume ordinary European/American users).
     - One primary long-tail keyword (e.g., “best affordable skincare routine”) and two secondary keywords (e.g., “natural skincare,” “daily beauty”) from titles, headings, meta descriptions, or frequent terms.
     - Brand tone (e.g., warm, authoritative) and values (e.g., affordability, quality) from website style and messaging.
     - Any slogan, unique selling points, special offers, or guarantees to integrate naturally.

2. SEO Optimization:
   - Integrate the primary keyword into the title, 2-3 subheadings, and body (4-5 times naturally), and each secondary keyword 2-3 times, ensuring seamless flow.
   - Create a 150-160 character meta description with the primary keyword, designed to intrigue readers and boost click-through rates.

3. Structure:
   - Introduction (~150-200 words): Hook readers with a relatable anecdote, surprising statistic, or pain-point question relevant to the audience, setting the stage for the offering.
   - Body:
     - Section 1: Offer detailed actionable tips or industry insights tied to the product/service (~500-600 words), providing in-depth value with examples or step-by-step advice.
     - Section 2: Present an extended relatable scenario or story showcasing the offering’s benefits (~400-500 words), using vivid details and emotional appeal.
     - Section 3: Discuss strengths and minor drawbacks with honesty and balance (~400-500 words), including comparisons to alternatives for context.
     - Section 4: Include 5 detailed, realistic customer testimonials or experiences (~300-400 words), each with a unique perspective or use case.
     - Section 5: Explore additional benefits or use cases of the offering (~300-400 words), broadening its appeal with practical scenarios.
   - Conclusion (~100-150 words): Recap key points, emphasize value, and include a subtle CTA, e.g., “Ready to see the difference? Visit [official website]({site}).”

4. Tone and Style:
   - Use a friendly, conversational tone matching the brand’s observed style (e.g., approachable, expert-led).
   - Incorporate storytelling throughout and pose 3+ open-ended questions (e.g., “How do you usually tackle [problem]?”) to deepen engagement.
   - Reflect the brand’s values (e.g., innovation, trustworthiness) and mention the slogan if identified, weaving it naturally into the narrative.

5. Natural Interest Generation:
   - Emphasize benefits and value over sales jargon, subtly nudging readers toward purchase.
   - Use varied soft CTAs like “Intrigued? Learn more at [official website]({site}),” “See it in action at [official website]({site}),” or “Find out why it’s a must-have at [official website]({site}).”
   - Add organic social proof, e.g., “Users swear by its ability to solve [specific issue]” or “It’s become a go-to for [audience].”

6. Enhancements:
   - Keep paragraphs short (3-5 sentences) for mobile readability.
   - Suggest 2 relevant images or infographics (e.g., “A chart showing [benefit over time],” “A before-and-after visual of [result]”) to break up text and enhance appeal.
   - End with an interactive reader prompt, e.g., “What’s your experience with [problem]? Let us know below!”

7. Category Selection:
Select one category that best aligns with {site} from the following list: Finance, Health & Beauty, Pets & Animals, Travel & Transportation, Auctions & Classifieds, Consumer Goods, Toys & Games, Business & Industrial, Sports & Fitness, Hobbies & Leisure, People & Society, Science & Reference, Gifts & Events, Eco-Friendly Products, Arts & Entertainment, Food & Drink, Jobs & Education, Fashion & Apparel, Home & Electronics, Others.

Deliver a natural, engaging, and detailed article free of robotic phrasing, tailored to European and American habits, and optimized for both purchases and SEO. Ensure the content feels comprehensive and flows seamlessly, avoiding unnecessary repetition.

List 4-5 core advantages of the brand, such as quality, innovation, environmental protection, service, etc. The description should be specific and vivid to highlight the brand's appeal.
List 2-3 minor shortcomings, such as slightly higher prices, limited coverage areas, etc. The wording should be gentle, emphasizing that the brand is improving or these shortcomings do not affect the overall experience.
Tone: Positive and friendly, inspire readers' trust and interest in the brand.
Length: Each column is about 150-200 words, concise and informative.
The left side is the "Advantages" column (green background), and the right side is the "Disadvantages" column (red background).
Use icons before list items (such as ✔ for advantages and ✘ for disadvantages).
The style should be beautiful and responsive, and support mobile display.
Ensure that the code is standardized and the structure is clear.

The article content is enhanced through a variety of markdown layout styles.

Format as JSON:
{
  "title": "A compelling, SEO-optimized title with the primary keyword, Written in English.",
  "meta_description": "A 150-160 character summary with the primary keyword, crafted to intrigue, Written in English.",
  "tags": ["primary keyword", "secondary keyword 1", "secondary keyword 2", "related term 1", "related term 2"],
  "category_name": "The selected category from the provided list"
  "content": "The article in Markdown format (2000-2500 words), the article content is enhanced through a variety of Markdown layout styles. Written in English. Do not include any metadata, image suggestions, tag suggestions, category suggestions, or non-article content (e.g., notes, placeholders, or instructions) within the 'content' field. Ensure the article is cohesive, well-researched, and optimized for SEO with the primary keyword naturally integrated.",
}'''


def generate_prompt(site: str) -> str:
    """生成提示词

    Args:
        site: 网站域名

    Returns:
        str: 生成的提示词
    """
    return ARTICLE_PROMPT.replace('{site}', site)
