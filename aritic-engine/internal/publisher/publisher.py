"""文章发布器"""
import base64
import requests
import os
from datetime import datetime
from abc import ABC, abstractmethod
from typing import Dict, Optional, List
from sqlalchemy import create_engine, text
from internal.storage.models import SiteConfig, MerchantInfo
from internal.external_gateway.wordpress_api import WordPress, utils
from internal.utils import timeutil
from tenacity import retry, stop_after_attempt, wait_exponential


class Publisher(ABC):
    """发布器基类"""

    def __init__(self, site_config: SiteConfig):
        """初始化发布器

        Args:
            site_config: 站点配置
        """
        self.site_config = site_config

    @abstractmethod
    def publish_article(self,
                        title: str,
                        content: str,
                        publish_date: str,
                        merchant_info: MerchantInfo,
                        logo: str,
                        brand_description: str,
                        excerpt: str,
                        image_url: str,
                        category_name: str,
                        tag_names: Optional[List[str]] = None,
                        ) -> bool:
        """发布文章

        Args:
            title: 文章标题
            content: 文章内容
            featured_image_url: 特色图片URL
            meta_description: 元描述
            meta_keywords: 关键词(SEO)
            categories: 分类名称列表
            tags: 标签名称列表

        Returns:
            bool: 是否发布成功
        """
        pass


class WordPressPublisher(Publisher):
    """WordPress 文章发布器"""

    def __init__(self, site_config: SiteConfig):
        super().__init__(site_config)

    def publish_article(self,
                        title: str,
                        content: str,
                        publish_date: str,
                        merchant_info: MerchantInfo,
                        logo: str,
                        brand_description: str,
                        excerpt: str,
                        image_url: str,
                        category_name: str,
                        tag_names: Optional[List[str]] = None,
                        ) -> bool:
        """发布文章到WordPress

        Args:
            title: 文章标题
            content: 文章内容(Markdown格式)
            featured_image_url: 特色图片URL
            meta_description: 元描述
            meta_keywords: 关键词(SEO)
            category_name: 分类名称列表
            tag_names: 标签名称列表

        Returns:
            bool: 是否发布成功
        """
        try:
            # 构建文章数据
            post_data = {
                'title': title,
                'content': content,
                'excerpt': excerpt,
                'status': 'publish',
                'date': timeutil.generate_worktime_datetime(publish_date),
                'slug': utils.title_to_slug(title),
                'format': 'standard',
            }

            wp = WordPress(
                url=self.site_config.site_url + self.site_config.site_api,
                username=self.site_config.site_username,
                password=self.site_config.site_password
            )
            # 处理分类
            if category_name:
                # 获取所有分类
                categories = wp.categories.list({'per_page': 100})
                print(categories)
                category_id = None

                # 查找是否存在该分类
                for category in categories:
                    if category['name'].replace("&amp;", "&").lower() == category_name.lower():
                        category_id = category['id']
                        break

                # 如果分类不存在，创建新分类
                if not category_id:
                    new_category = wp.categories.create({
                        'name': category_name,
                        'slug': category_name.lower().replace(' & ', '-').replace(' ', '-')
                    })
                    category_id = new_category['id']

                post_data['categories'] = [category_id]
            print("分类处理完毕")
            # 处理标签
            try:
                if tag_names:
                    tag_ids = []
                    existing_tags = wp.tags.list({'per_page': 100})
                    existing_tag_dict = {tag['name']: tag['id'] for tag in existing_tags}

                    for tag_name in tag_names:
                        # 检查标签是否存在
                        if tag_name in existing_tag_dict:
                            tag_ids.append(existing_tag_dict[tag_name])
                        else:
                            # 创建新标签
                            new_tag = wp.tags.create({
                                'name': tag_name,
                                'slug': tag_name.lower().replace(' & ', '-').replace(' ', '-')
                            })
                            tag_ids.append(new_tag['id'])

                    post_data['tags'] = tag_ids
            except:
                pass
            print("tag处理完毕")
            # 处理特色图片
            if image_url:
                # 上传图片到媒体库
                media = wp.media.create_from_url(
                    url=image_url,  # 替换为实际可用的图片URL
                    backup_url=merchant_info.website_screenshot,
                    title=title,
                    caption='featured image-'+title,
                    alt_text=title
                )
                post_data['featured_media'] = media['id']
            print("图片处理完毕")
            # 发布文章
            print("开始发布文章：", post_data)
            new_post = wp.posts.create(post_data)
            print(f"创建成功！文章: {new_post}")

            # 创建品牌联盟信息
            brand_data = {
                'brand_name': merchant_info.brand_name,
                'brand_logo': logo,
                'brand_website': merchant_info.affiliate_url,
                'brand_description': excerpt,
            }
            wp.plugin_brand_affiliate.create(new_post['id'], brand_data)
            print("创建品牌联盟")
            return True
        except Exception as e:
            return False


class PostgresPublisher(Publisher):
    """PostgreSQL数据库发布器"""

    def __init__(self, site_config: SiteConfig):
        """初始化PostgreSQL发布器

        Args:
            site_config: 站点配置，其中：
                - site_url: 数据库host:port
                - site_api: 数据库名
                - site_username: 数据库用户名
                - site_password: 数据库密码
        """
        super().__init__(site_config)
        # 创建数据库连接引擎
        db_url = f"postgresql://{site_config.site_username}:{site_config.site_password}@{site_config.site_url}/{site_config.site_api}"
        self.engine = create_engine(
            db_url,
            pool_size=10,
            max_overflow=20,
            pool_timeout=30,
            pool_recycle=1800
        )

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def publish_article(self,
                       title: str,
                       content: str,
                       publish_date: str,
                       merchant_info: MerchantInfo,
                       logo: str,
                       brand_description: str,
                       excerpt: str,
                       image_url: str,
                       category_name: str,
                       tag_names: Optional[List[str]] = None,
                       ) -> bool:
        """发布文章到PostgreSQL数据库

        Args:
            title: 文章标题
            content: 文章内容
            publish_date: 发布日期
            merchant_info: 商家信息
            logo: 品牌logo
            brand_description: 品牌描述
            excerpt: 文章摘要
            image_url: 特色图片URL
            category_name: 分类名称
            tag_names: 标签名称列表

        Returns:
            bool: 是否发布成功
        """
        try:
            # 准备SQL语句
            sql = text("""
                INSERT INTO blogs (
                    title, description, content, featured_image,
                    published_at, category, tags, merchant_id,
                    created_at, updated_at
                ) VALUES (
                    :title, :description, :content, :featured_image,
                    :published_at, :category, :tags, :merchant_id,
                    :created_at, :updated_at
                )
            """)

            # 准备参数
            now = datetime.now()
            params = {
                "title": title,
                "description": excerpt,  # description字段使用文章摘要
                "content": content,
                "featured_image": image_url,  # featured_image字段使用特色图片URL
                "published_at": datetime.strptime(publish_date, '%Y-%m-%d %H:%M:%S'),
                "category": category_name,
                "tags": tag_names if tag_names else None,
                "merchant_id": merchant_info.id,
                "created_at": now,
                "updated_at": now
            }

            # 执行SQL
            with self.engine.connect() as conn:
                conn.execute(sql, params)
                conn.commit()
            return True

        except Exception as e:
            raise Exception(f"发布文章到PostgreSQL失败: {str(e)}")

class CustomAPIPublisher(Publisher):
    """自定义API文章发布器"""

    def __init__(self, site_config: SiteConfig):
        super().__init__(site_config)
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.site_config.site_password}'
        }
        # 设置保存文章的基础路径
        self.base_save_path = "/Users/<USER>/projects/aritic-engine/posts"

    def publish_article(self,
                        title: str,
                        content: str,
                        publish_date: str,
                        merchant_info: MerchantInfo,
                        logo: str,
                        brand_description: str,
                        excerpt: str,
                        image_url: str,
                        category_name: str,
                        tag_names: Optional[List[str]] = None,
                        ) -> bool:
        """发布文章到自定义API

        Args:
            title: 文章标题
            content: 文章内容
            featured_image_url: 特色图片URL
            meta_description: 元描述
            meta_keywords: 关键词(SEO)
            categories: 分类名称列表
            tags: 标签名称列表

        Returns:
            bool: 是否发布成功
        """
        try:
            # 构建品牌信息
            brand = {
                "name": merchant_info.brand_name,  # 使用site_username作为品牌名称
                "website": merchant_info.affiliate_url,  # 使用site_url作为品牌网站
                "description": brand_description,
                "logo": logo
            }

            # 构建请求数据，直接发送Markdown格式内容
            post_data = {
                "title": title,
                "excerpt": excerpt,
                "image": image_url,
                "category": category_name,  # 使用第一个分类
                "tags": tag_names or [],
                "brand": brand,
                "content": content,  # 直接发送Markdown内容
                "publish_date": publish_date,
            }
            print(post_data)
            
            # 保存文章到本地Markdown文件
            self._save_to_markdown(
                title=title,
                content=content,
                publish_date=publish_date,
                merchant_info=merchant_info,
                logo=logo,
                brand_description=brand_description,
                excerpt=excerpt,
                image_url=image_url,
                category_name=category_name,
                tag_names=tag_names
            )
            
            # 发布文章
            response = requests.post(
                f"{self.site_config.site_url}/api/posts",
                headers=self.headers,
                json=post_data
            )

            return response.status_code in [200, 201]

        except Exception as e:
            print(f"发布文章失败: {str(e)}")
            return False
            
    def _save_to_markdown(self,
                         title: str,
                         content: str,
                         publish_date: str,
                         merchant_info: MerchantInfo,
                         logo: str,
                         brand_description: str,
                         excerpt: str,
                         image_url: str,
                         category_name: str,
                         tag_names: Optional[List[str]] = None,
                         ) -> bool:
        """将文章保存为本地Markdown文件
        
        Args:
            title: 文章标题
            content: 文章内容
            publish_date: 发布日期
            merchant_info: 商家信息
            logo: 品牌logo
            brand_description: 品牌描述
            excerpt: 文章摘要
            image_url: 特色图片URL
            category_name: 分类名称
            tag_names: 标签名称列表
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 创建保存目录（如果不存在）
            save_dir = os.path.join(self.base_save_path, str(merchant_info.site_id))
            os.makedirs(save_dir, exist_ok=True)
            
            # 生成文件名（使用标题的slug形式）
            file_name = utils.title_to_slug(title) + ".md"
            file_path = os.path.join(save_dir, file_name)
            
            # 格式化发布日期为示例格式：YYYY-MM-DD HH:MM:SS
            formatted_date = publish_date
            if not ":" in publish_date:
                # 如果日期格式不包含时间，添加时间部分
                formatted_date = f"{publish_date} 00:00:00"
            
            # 构建标签列表的正确格式
            tags_str = ""
            if tag_names and len(tag_names) > 0:
                # 将每个标签用双引号包围
                quoted_tags = ["\"{}\"".format(tag) for tag in tag_names]
                tags_str = f"[{', '.join(quoted_tags)}]"
            
            # 构建品牌信息的正确格式
            brand_info = {
                "name": merchant_info.brand_name,
                "logo": logo,
                "website": merchant_info.affiliate_url,
                "description": brand_description
            }
            
            # 构建Markdown文件内容，与示例格式保持一致
            md_content = f"---\n"
            md_content += f"title: \"{title}\"\n"
            md_content += f"excerpt: \"{excerpt}\"\n"
            md_content += f"publishDate: {formatted_date}\n"
            md_content += f"image: \"{image_url}\"\n"
            md_content += f"category: \"{category_name}\"\n"
            if tags_str:
                md_content += f"tags: {tags_str}\n"
            
            # 添加品牌信息，格式与示例一致
            md_content += f"brand: {{\n"
            md_content += f"  name: \"{brand_info['name']}\", \n"
            md_content += f"  logo: \"{brand_info['logo']}\", \n"
            md_content += f"  website: \"{brand_info['website']}\", \n"
            md_content += f"  description: \"{brand_info['description']}\" \n"
            md_content += f"}}\n"
            md_content += f"---\n"
            md_content += content
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
                
            print(f"文章已保存到: {file_path}")
            return True
            
        except Exception as e:
            print(f"保存文章到Markdown文件失败: {str(e)}")
            return False
