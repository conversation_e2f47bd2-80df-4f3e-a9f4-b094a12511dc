import socket
import psutil
from typing import Optional, Tuple


def is_port_in_use(port: int, host: str = '127.0.0.1') -> bool:
    """检查指定端口是否被占用

    Args:
        port: 要检查的端口号
        host: 主机地址，默认为localhost

    Returns:
        bool: 如果端口被占用返回True，否则返回False
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result == 0
    except:
        return True


def find_free_port(start_port: int = 6000, end_port: int = 7000, host: str = '127.0.0.1') -> Optional[int]:
    """在指定范围内查找可用端口

    Args:
        start_port: 起始端口号
        end_port: 结束端口号
        host: 主机地址，默认为localhost

    Returns:
        Optional[int]: 找到的可用端口号，如果没有找到则返回None
    """
    for port in range(start_port, end_port + 1):
        if not is_port_in_use(port, host):
            return port
    return None


def get_process_by_port(port: int) -> Optional[Tuple[int, str]]:
    """获取占用指定端口的进程信息

    Args:
        port: 端口号

    Returns:
        Optional[Tuple[int, str]]: 进程ID和进程名称的元组，如果没有找到则返回None
    """
    try:
        for proc in psutil.process_iter(['pid', 'name', 'connections']):
            try:
                for conn in proc.connections():
                    if conn.laddr.port == port:
                        return proc.pid, proc.name()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except:
        pass
    return None


def kill_process_by_port(port: int) -> bool:
    """终止占用指定端口的进程

    Args:
        port: 端口号

    Returns:
        bool: 如果成功终止进程返回True，否则返回False
    """
    try:
        process_info = get_process_by_port(port)
        if process_info:
            pid, _ = process_info
            psutil.Process(pid).terminate()
            return True
    except:
        pass
    return False
