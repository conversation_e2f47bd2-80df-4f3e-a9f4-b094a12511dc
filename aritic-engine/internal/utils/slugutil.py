import re
import uuid
from typing import Optional, Set
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import logging

logger = logging.getLogger(__name__)


def title_to_slug(title: str) -> str:
    """将标题转换为 slug

    Args:
        title: 文章标题

    Returns:
        str: 转换后的 slug
    """
    if not title:
        return ""

    # 转换为小写
    slug = title.lower()

    # 替换中文字符为拼音或移除（简单处理：移除非ASCII字符）
    # 只保留字母、数字、空格和连字符
    slug = re.sub(r'[^\w\s-]', ' ', slug, flags=re.ASCII)

    # 将多个空格替换为单个连字符
    slug = "-".join(word for word in slug.split() if word)

    # 将多个连字符替换为单个连字符
    slug = re.sub(r'-+', '-', slug)

    # 移除首尾的连字符
    slug = slug.strip("-")

    # 如果处理后为空，使用默认值
    if not slug:
        slug = "article"

    # 限制长度为 250 个字符（数据库字段限制为 300）
    if len(slug) > 250:
        slug = slug[:250].rstrip("-")

    return slug


def generate_unique_slug(title: str, db_engine=None, existing_slugs: Optional[Set[str]] = None) -> str:
    """生成唯一的 slug
    
    Args:
        title: 文章标题
        db_engine: 数据库引擎（可选，用于检查数据库中的唯一性）
        existing_slugs: 已存在的 slug 集合（可选，用于内存检查）
        
    Returns:
        str: 唯一的 slug
    """
    base_slug = title_to_slug(title)
    
    if not base_slug:
        # 如果标题无法生成有效的 slug，使用随机字符串
        base_slug = f"article-{uuid.uuid4().hex[:8]}"
    
    # 如果没有提供检查方式，直接返回基础 slug
    if not db_engine and not existing_slugs:
        return base_slug
    
    # 检查 slug 是否已存在
    if _is_slug_unique(base_slug, db_engine, existing_slugs):
        return base_slug
    
    # 如果 slug 已存在，添加数字后缀
    counter = 1
    while True:
        new_slug = f"{base_slug}-{counter}"
        if _is_slug_unique(new_slug, db_engine, existing_slugs):
            return new_slug
        counter += 1
        
        # 防止无限循环
        if counter > 1000:
            # 使用随机字符串作为后缀
            random_suffix = uuid.uuid4().hex[:8]
            return f"{base_slug}-{random_suffix}"


def _is_slug_unique(slug: str, db_engine=None, existing_slugs: Optional[Set[str]] = None) -> bool:
    """检查 slug 是否唯一
    
    Args:
        slug: 要检查的 slug
        db_engine: 数据库引擎（可选）
        existing_slugs: 已存在的 slug 集合（可选）
        
    Returns:
        bool: 如果 slug 唯一返回 True，否则返回 False
    """
    # 首先检查内存中的集合
    if existing_slugs and slug in existing_slugs:
        return False
    
    # 然后检查数据库
    if db_engine:
        try:
            with db_engine.connect() as conn:
                result = conn.execute(
                    text("SELECT COUNT(*) FROM articles WHERE slug = :slug"),
                    {"slug": slug}
                )
                count = result.scalar()
                return count == 0
        except SQLAlchemyError as e:
            logger.warning(f"检查 slug 唯一性时数据库错误: {str(e)}")
            # 如果数据库检查失败，假设 slug 是唯一的
            return True
    
    return True


def validate_slug(slug: str) -> bool:
    """验证 slug 格式是否正确
    
    Args:
        slug: 要验证的 slug
        
    Returns:
        bool: 如果格式正确返回 True，否则返回 False
    """
    if not slug:
        return False
    
    # slug 应该只包含小写字母、数字和连字符
    # 不能以连字符开头或结尾
    # 不能包含连续的连字符
    pattern = r'^[a-z0-9]+(?:-[a-z0-9]+)*$'
    
    if not re.match(pattern, slug):
        return False
    
    # 检查长度
    if len(slug) < 3 or len(slug) > 300:
        return False
    
    return True


def sanitize_slug(slug: str) -> str:
    """清理和标准化 slug

    Args:
        slug: 原始 slug

    Returns:
        str: 清理后的 slug
    """
    if not slug:
        return ""

    # 转换为小写
    slug = slug.lower()

    # 只保留字母、数字和连字符（ASCII字符）
    slug = re.sub(r'[^a-z0-9-]', '-', slug)

    # 将多个连字符替换为单个连字符
    slug = re.sub(r'-+', '-', slug)

    # 移除首尾的连字符
    slug = slug.strip('-')

    # 如果处理后为空，返回默认值
    if not slug:
        slug = "article"

    # 限制长度
    if len(slug) > 250:
        slug = slug[:250].rstrip('-')

    return slug


# 测试函数
if __name__ == "__main__":
    test_titles = [
        "Hello World!",
        "This is a Test Title",
        "Multiple   Spaces   Here",
        "Special@#Characters&Here",
        "中文标题测试",
        "Mixed 中英文 Title",
        "Very Long Title That Should Be Truncated Because It Exceeds The Maximum Length Limit For Slugs In The Database Schema Which Is Set To 300 Characters But We Want To Keep It Under 250 To Be Safe And Allow For Potential Suffixes",
    ]
    
    for title in test_titles:
        slug = title_to_slug(title)
        print(f"Original: {title}")
        print(f"Slug: {slug}")
        print(f"Valid: {validate_slug(slug)}")
        print("-" * 50)
