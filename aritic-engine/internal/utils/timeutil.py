import random


def generate_worktime_datetime(original_date):
    """
    生成一个工作时间的日期时间字符串
    参数：
        original_date: 原始日期字符串
    返回：
        str: 格式为'2025-03-26 HH:MM:SS'的工作时间字符串
    """
    # 处理输入日期，提取日期部分
    if " " in original_date:  # 如果包含时间部分
        base_date = original_date.split(" ")[0]  # 只取日期部分
    else:
        base_date = original_date

    # 随机生成工作时间 (9:00 - 17:00)
    hour = random.randint(9, 16)  # 9到16点 (17点是下班时间)
    minute = random.randint(0, 59)  # 0到59分钟
    second = random.randint(0, 59)  # 0到59秒
    # 格式化时间
    return f"{base_date} {hour:02d}:{minute:02d}:{second:02d}"
