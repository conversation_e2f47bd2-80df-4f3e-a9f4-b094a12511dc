import requests
import json


def fetch_brand_data(domain, default_brand="Unknown Brand"):
    """
    获取品牌信息

    Args:
        domain (str): 要查询的域名
        default_brand (str): 获取失败时的默认品牌名称，默认为"Unknown Brand"

    Returns:
        str: 处理后的品牌名称或默认品牌名称
    """
    url = f"https://api.brandfetch.io/v2/search/{domain}?c=1idzI375egayQVW-c_2"

    try:
        response = requests.get(url)
        response.raise_for_status()  # 如果不是200状态码，会抛出异常

        json_data = response.json()
        # 检查返回的是否为数组且有数据
        if isinstance(json_data, list) and len(json_data) > 0 and 'name' in json_data[0]:
            brand_name = json_data[0]['name']
            # 处理品牌名称：去掉 "--"，去除前后空格
            cleaned_name = brand_name.replace("--", "").strip()
            return cleaned_name if cleaned_name else default_brand
        return default_brand

    except requests.exceptions.RequestException as e:
        print(f"Error for {domain}: HTTP {e.response.status_code if e.response else 'unknown'}")
        return default_brand
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        print(f"Error for {domain}: {str(e)}")
        return default_brand
    except Exception as e:
        print(f"Error for {domain}: {str(e)}")
        return default_brand


# 使用示例
if __name__ == "__main__":
    # 测试用例1：正常情况
    result1 = fetch_brand_data("lycamobile.it")
    print(f"Result 1: {result1}")

    # 测试用例2：自定义默认品牌
    result2 = fetch_brand_data("lpgit.com", "lpgit Brand")
    print(f"Result 2: {result2}")

    # 模拟原始使用方式
    result = fetch_brand_data("lycamobile.it")
    if result != "Unknown Brand":  # 简单判断是否成功
        print(f"Successfully got brand: {result}")
    else:
        print("Failed to get brand, using default")