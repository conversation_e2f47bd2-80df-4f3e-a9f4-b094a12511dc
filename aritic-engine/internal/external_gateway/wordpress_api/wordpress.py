import requests
from .endpoints.posts import Posts
from .endpoints.tags import Tags
from .endpoints.categories import Categories
from .endpoints.media import Media
from .endpoints.plugin_brand_affiliate import PluginBrandAffiliate
from .auth import Auth


class WordPress:
    """
    WordPress API客户端主类

    用于与WordPress REST API交互的主要入口点。

    Args:
        url (str): WordPress站点的REST API基础URL，通常是 https://example.com/wp-json
        username (str, optional): WordPress用户名
        password (str, optional): WordPress应用程序密码
    """

    def __init__(self, url, username=None, password=None):
        # 确保URL以/结尾
        if not url.endswith('/'):
            url += '/'

        self.url = url
        self.auth = Auth(username, password)
        self.session = requests.Session()

        # 初始化各个端点
        self.posts = Posts(self)
        self.tags = Tags(self)
        self.categories = Categories(self)
        self.media = Media(self)
        self.plugin_brand_affiliate = PluginBrandAffiliate(self)

    def get_url(self):
        """
        获取API基础URL

        Returns:
            str: API基础URL
        """
        return self.url

    def get_auth(self):
        """
        获取认证对象

        Returns:
            Auth: 认证对象
        """
        return self.auth