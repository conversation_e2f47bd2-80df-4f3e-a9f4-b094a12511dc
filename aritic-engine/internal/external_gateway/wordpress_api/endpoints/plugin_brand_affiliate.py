from .base import BaseEndpoint


class PluginBrandAffiliate(BaseEndpoint):
    """
    处理WordPress BrandAffiliate 插件相关的API端点
    Args:
        wordpress: WordPress API客户端实例
    """

    def __init__(self, wordpress):
        super().__init__(wordpress)
        self.endpoint = 'plugins/brand-affiliate'

    def get(self, post_id, params=None):
        """
        获取单个文章

        Args:
            post_id (int): 文章ID
            params (dict, optional): 查询参数

        Returns:
            dict: 文章详情
        """
        return super().get(f"{self.endpoint}/{post_id}", params)

    def create(self, post_id, data):
        """
        创建文章

        Args:
            data (dict): 文章数据，可包含以下字段：
                - brand_name: 品牌名字
                - brand_logo: 品牌logo
                - brand_website: 网站
                - brand_description: 描述

        Returns:
            dict: 创建的文章详情
        """
        return super().post(f"{self.endpoint}/{post_id}", data)

    def update(self, post_id, data):
        """
        更新文章

        Args:
            post_id (int): 文章ID
            data (dict): 要更新的文章数据

        Returns:
            dict: 更新后的文章详情
        """
        return super().put(f"{self.endpoint}/{post_id}", data)

