from .base import BaseEndpoint

class Tags(BaseEndpoint):
    """
    处理WordPress标签(Tags)相关的API端点
    
    提供获取、创建、更新和删除标签的方法。
    
    Args:
        wordpress: WordPress API客户端实例
    """
    
    def __init__(self, wordpress):
        super().__init__(wordpress)
        self.endpoint = 'tags'
    
    def list(self, params=None):
        """
        获取标签列表
        
        Args:
            params (dict, optional): 查询参数，可包含以下选项：
                - page: 页码
                - per_page: 每页数量
                - search: 搜索关键词
                - hide_empty: 是否隐藏空标签
                
        Returns:
            list: 标签列表
        """
        return super().get(self.endpoint, params)
    
    def get(self, tag_id, params=None):
        """
        获取单个标签
        
        Args:
            tag_id (int): 标签ID
            params (dict, optional): 查询参数
            
        Returns:
            dict: 标签详情
        """
        return super().get(f"{self.endpoint}/{tag_id}", params)
    
    def create(self, data):
        """
        创建标签
        
        Args:
            data (dict): 标签数据，可包含以下字段：
                - name: 标签名称（必填）
                - description: 标签描述
                - slug: 标签别名
                
        Returns:
            dict: 创建的标签详情
        """
        return super().post(self.endpoint, data)
    
    def update(self, tag_id, data):
        """
        更新标签
        
        Args:
            tag_id (int): 标签ID
            data (dict): 要更新的标签数据
            
        Returns:
            dict: 更新后的标签详情
        """
        return super().put(f"{self.endpoint}/{tag_id}", data)
    
    def delete(self, tag_id, force=False):
        """
        删除标签
        
        Args:
            tag_id (int): 标签ID
            force (bool, optional): 是否强制删除，默认为False
            
        Returns:
            dict: 删除操作的结果
        """
        params = {'force': force}
        return super().delete(f"{self.endpoint}/{tag_id}", params)