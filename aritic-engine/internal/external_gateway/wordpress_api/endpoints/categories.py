from .base import BaseEndpoint

class Categories(BaseEndpoint):
    """
    处理WordPress分类(Categories)相关的API端点
    
    提供获取、创建、更新和删除分类的方法。
    
    Args:
        wordpress: WordPress API客户端实例
    """
    
    def __init__(self, wordpress):
        super().__init__(wordpress)
        self.endpoint = 'categories'
    
    def list(self, params=None):
        """
        获取分类列表
        
        Args:
            params (dict, optional): 查询参数，可包含以下选项：
                - page: 页码
                - per_page: 每页数量
                - search: 搜索关键词
                - hide_empty: 是否隐藏空分类
                - parent: 父分类ID
                - post: 文章ID，获取与特定文章关联的分类
                
        Returns:
            list: 分类列表
        """
        return super().get(self.endpoint, params)
    
    def get(self, category_id, params=None):
        """
        获取单个分类
        
        Args:
            category_id (int): 分类ID
            params (dict, optional): 查询参数
            
        Returns:
            dict: 分类详情
        """
        return super().get(f"{self.endpoint}/{category_id}", params)
    
    def create(self, data):
        """
        创建分类
        
        Args:
            data (dict): 分类数据，可包含以下字段：
                - name: 分类名称（必填）
                - description: 分类描述
                - slug: 分类别名
                - parent: 父分类ID
                
        Returns:
            dict: 创建的分类详情
        """
        return super().post(self.endpoint, data)
    
    def update(self, category_id, data):
        """
        更新分类
        
        Args:
            category_id (int): 分类ID
            data (dict): 要更新的分类数据
            
        Returns:
            dict: 更新后的分类详情
        """
        return super().put(f"{self.endpoint}/{category_id}", data)
    
    def delete(self, category_id, force=False):
        """
        删除分类
        
        Args:
            category_id (int): 分类ID
            force (bool, optional): 是否强制删除，默认为False
            
        Returns:
            dict: 删除操作的结果
        """
        params = {'force': force}
        return super().delete(f"{self.endpoint}/{category_id}", params)