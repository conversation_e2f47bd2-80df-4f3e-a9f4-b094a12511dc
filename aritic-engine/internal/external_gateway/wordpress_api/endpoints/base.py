import requests
import json


class BaseEndpoint:
    """
    所有WordPress API端点的基类

    提供通用的HTTP请求方法和错误处理。

    Args:
        wordpress: WordPress API客户端实例
    """

    def __init__(self, wordpress):
        self.wordpress = wordpress
        self.base_url = wordpress.get_url()

    def _get_headers(self):
        """
        获取HTTP请求头

        Returns:
            dict: HTTP请求头字典
        """
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        # 添加认证头
        auth_headers = self.wordpress.get_auth().get_auth_header()
        headers.update(auth_headers)

        return headers

    def _handle_response(self, response):
        """
        处理API响应

        Args:
            response: requests响应对象

        Returns:
            dict or list: 解析后的JSON响应

        Raises:
            Exception: 当API返回错误时抛出
        """
        print(response.json())
        if response.status_code >= 200 and response.status_code < 300:
            if response.content:
                return response.json()
            return {}
        else:
            try:
                error = response.json()
                message = error.get('message', 'Unknown error')
                code = error.get('code', 'unknown')
                raise Exception(f"WordPress API error: {code} - {message}")
            except json.JSONDecodeError:
                raise Exception(f"WordPress API error: {response.status_code} - {response.text}")

    def get(self, endpoint, params=None):
        """
        发送GET请求

        Args:
            endpoint (str): API端点路径
            params (dict, optional): 查询参数

        Returns:
            dict or list: API响应
        """
        url = f"{self.base_url}wp/v2/{endpoint}"
        headers = self._get_headers()
        auth = None
        if self.wordpress.get_auth().has_credentials():
            auth = (self.wordpress.get_auth().get_username(), self.wordpress.get_auth().get_password())
        response = requests.get(url, headers=headers, params=params, auth=auth)
        print(response.json())
        return self._handle_response(response)

    def post(self, endpoint, data):
        """
        发送POST请求

        Args:
            endpoint (str): API端点路径
            data (dict): 请求数据

        Returns:
            dict: API响应
        """
        url = f"{self.base_url}wp/v2/{endpoint}"
        headers = self._get_headers()
        print(url, data)
        auth = None
        if self.wordpress.get_auth().has_credentials():
            auth = (self.wordpress.get_auth().get_username(), self.wordpress.get_auth().get_password())
        response = requests.post(url, headers=headers, json=data, auth=auth)
        return self._handle_response(response)

    def put(self, endpoint, data):
        """
        发送PUT请求

        Args:
            endpoint (str): API端点路径
            data (dict): 请求数据

        Returns:
            dict: API响应
        """
        url = f"{self.base_url}wp/v2/{endpoint}"
        headers = self._get_headers()

        auth = None
        if self.wordpress.get_auth().has_credentials():
            auth = (self.wordpress.get_auth().get_username(), self.wordpress.get_auth().get_password())
        response = requests.put(url, headers=headers, json=data, auth=auth)
        return self._handle_response(response)

    def delete(self, endpoint, params=None):
        """
        发送DELETE请求

        Args:
            endpoint (str): API端点路径
            params (dict, optional): 查询参数

        Returns:
            dict: API响应
        """
        url = f"{self.base_url}wp/v2/{endpoint}"
        headers = self._get_headers()

        auth = None
        if self.wordpress.get_auth().has_credentials():
            auth = (self.wordpress.get_auth().get_username(), self.wordpress.get_auth().get_password())
        response = requests.delete(url, headers=headers, params=params, auth=auth)
        return self._handle_response(response)
