from .base import BaseEndpoint

class Posts(BaseEndpoint):
    """
    处理WordPress文章(Posts)相关的API端点
    
    提供获取、创建、更新和删除文章的方法。

    官方文档：https://developer.wordpress.org/rest-api/reference/posts/#list-posts
    
    Args:
        wordpress: WordPress API客户端实例
    """
    
    def __init__(self, wordpress):
        super().__init__(wordpress)
        self.endpoint = 'posts'
    
    def list(self, params=None):
        """
        获取文章列表
        
        Args:
            params (dict, optional): 查询参数，可包含以下选项：
                - page: 页码，Default: 1
                - per_page: 每页数量，Default: 10
                - search: 搜索关键词
                - after: 在此日期之后发布的文章，Limit response to posts published after a given ISO8601 compliant date.
                - modified_after：Limit response to posts modified after a given ISO8601 compliant date.
                - author: 作者ID，Limit result set to posts assigned to specific authors.
                - author_exclude：Ensure result set excludes posts assigned to specific authors.
                - before：在此日期之前发布的文章，Limit response to posts published after a given ISO8601 compliant date.
                - modified_before：Limit response to posts modified before a given ISO8601 compliant date.
                - exclude：Ensure result set excludes specific IDs.
                - include：Limit result set to specific IDs.
                - offset：Offset the result set by a specific number of items.
                - order：Order sort attribute ascending or descending.Default: desc
                - orderby：Sort collection by post attribute.Default: date
                - search_columns：Array of column names to be searched.
                - slug：Limit result set to posts with one or more specific slugs.
                - tax_relation：Limit result set based on relationship between multiple taxonomies.
                - categories: 分类ID列表，Limit result set to items with specific terms assigned in the categories taxonomy.
                - categories_exclude：Limit result set to items except those with specific terms assigned in the categories taxonomy.
                - tags: 标签ID列表，Limit result set to items with specific terms assigned in the tags taxonomy.
                - tags_exclude：Limit result set to items except those with specific terms assigned in the tags taxonomy.
                - sticky：Limit result set to items that are sticky.
                - status: 文章状态，Limit result set to posts assigned one or more statuses.
                - context： Default: view
                
        Returns:
            list: 文章列表
        """
        return super().get(self.endpoint, params)
    
    def get(self, post_id, params=None):
        """
        获取单个文章
        
        Args:
            post_id (int): 文章ID
            params (dict, optional): 查询参数
            
        Returns:
            dict: 文章详情
        """
        return super().get(f"{self.endpoint}/{post_id}", params)
    
    def create(self, data):
        """
        创建文章
        
        Args:
            data (dict): 文章数据，可包含以下字段：
                - title: 标题
                - content: 内容
                - excerpt: 摘要
                - status: 状态，如'publish', 'draft', 'pending', 'future', 'private'
                - date: The date the post was published, in the site's timezone.
                - date_gmt: The date the post was published, as GMT.
                - slug: An alphanumeric identifier for the post unique to its type.
                - password: A password to protect access to the content and excerpt.
                - title: The title for the post.
                - content: The content for the post.
                - author: The ID for the author of the post.
                - excerpt: The excerpt for the post.
                - featured_media: The ID of the featured media for the post.
                - comment_status: Whether or not comments are open on the post.
                - ping_status: Whether or not the post can be pinged.
                - format: The format for the post.
                - meta: Meta fields.
                - sticky: Whether or not the post should be treated as sticky.
                - template: The theme file to use to display the post.
                - categories: he terms assigned to the post in the category taxonomy.
                - tags: The terms assigned to the post in the post_tag taxonomy.
                
        Returns:
            dict: 创建的文章详情
        """
        print(self.endpoint, data)
        return super().post(self.endpoint, data)
    
    def update(self, post_id, data):
        """
        更新文章
        
        Args:
            post_id (int): 文章ID
            data (dict): 要更新的文章数据
            
        Returns:
            dict: 更新后的文章详情
        """
        return super().put(f"{self.endpoint}/{post_id}", data)
    
    def delete(self, post_id, force=False):
        """
        删除文章
        
        Args:
            post_id (int): 文章ID
            force (bool, optional): 是否强制删除，默认为False
            
        Returns:
            dict: 删除操作的结果
        """
        params = {'force': force}
        return super().delete(f"{self.endpoint}/{post_id}", params)