from .base import BaseEndpoint
import time
import requests
from PIL import Image
import io
import os
import tempfile
from urllib.parse import urlparse
import time


class Media(BaseEndpoint):
    """
    处理WordPress媒体(Media)相关的API端点
    
    提供获取、上传和更新媒体文件的方法。
    
    Args:
        wordpress: WordPress API客户端实例
    """

    def __init__(self, wordpress):
        super().__init__(wordpress)
        self.endpoint = 'media'

    def list(self, params=None):
        """
        获取媒体列表
        
        Args:
            params (dict, optional): 查询参数，可包含以下选项：
                - page: 页码
                - per_page: 每页数量
                - search: 搜索关键词
                - after: 在此日期之后上传的媒体
                - author: 作者ID
                - media_type: 媒体类型，如'image', 'video', 'audio', 'application'
                - mime_type: MIME类型
                
        Returns:
            list: 媒体列表
        """
        return super().get(self.endpoint, params)

    def get(self, media_id, params=None):
        """
        获取单个媒体项
        
        Args:
            media_id (int): 媒体ID
            params (dict, optional): 查询参数
            
        Returns:
            dict: 媒体详情
        """
        return super().get(f"{self.endpoint}/{media_id}", params)

    def create(self, data, file_path=None):
        """
        上传媒体文件
        
        Args:
            data (dict): 媒体数据，可包含以下字段：
                - title: 标题
                - caption: 说明
                - alt_text: 替代文本
                - description: 描述
            file_path (str, optional): 要上传的文件路径，如果提供，将使用multipart/form-data上传
                
        Returns:
            dict: 创建的媒体详情
        """
        if file_path:
            # 使用multipart/form-data上传文件
            url = f"{self.base_url}wp/v2/{self.endpoint}"
            headers = self._get_headers()
            # 移除Content-Type头，让requests自动设置正确的multipart/form-data
            if 'Content-Type' in headers:
                del headers['Content-Type']

            files = {'file': open(file_path, 'rb')}
            response = self.wordpress.session.post(url, headers=headers, data=data, files=files)
            return self._handle_response(response)
        else:
            # 使用JSON API创建媒体（通常用于外部URL）
            return self.post(self.endpoint, data)

    def update(self, media_id, data):
        """
        更新媒体项
        
        Args:
            media_id (int): 媒体ID
            data (dict): 要更新的媒体数据
            
        Returns:
            dict: 更新后的媒体详情
        """
        return self.put(f"{self.endpoint}/{media_id}", data)

    def delete(self, media_id, force=False):
        """
        删除媒体项
        
        Args:
            media_id (int): 媒体ID
            force (bool, optional): 是否强制删除，默认为False
            
        Returns:
            dict: 删除操作的结果
        """
        params = {'force': force}
        return super().delete(f"{self.endpoint}/{media_id}", params)

    def download_image(self, image_url, max_retries=3, retry_delay=2):
        """
        从URL下载图片

        Args:
            image_url (str): 图片URL
            max_retries (int, optional): 最大重试次数，默认为3
            retry_delay (int, optional): 重试延迟时间（秒），默认为2

        Returns:
            requests.Response: 下载成功返回response对象，失败返回None
        """

        for attempt in range(max_retries):
            try:
                response = requests.get(image_url, stream=True)
                response.raise_for_status()

                # 检查图片尺寸
                image_data = io.BytesIO(response.content)
                with Image.open(image_data) as img:
                    width, height = img.size
                    if width == 400 and height == 300:
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                return response
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                return None
        return None

    def create_from_url(self, url, backup_url=None, **data):
        """
        从URL创建媒体文件
        
        Args:
            url (str): 媒体文件的URL
            backup_url (str, optional): 备用URL，当主URL失败时使用
            **data: 其他媒体数据，可包含以下字段：
                - title: 标题
                - caption: 说明
                - alt_text: 替代文本
                - description: 描述
                - convert_to_webp (bool): 是否转换为webp格式，默认为True
                - webp_quality (int): webp压缩质量，范围1-100，默认为80
                
        Returns:
            dict: 创建的媒体详情
        """

        # 尝试从主URL下载
        response = self.download_image(url)

        # 如果主URL失败且有备用URL，尝试备用URL
        if response is None and backup_url:
            response = self.download_image(backup_url)
            if response is None:
                raise Exception("无法从主URL和备用URL下载图片")
            url = backup_url  # 更新URL以便获取正确的文件名
        elif response is None:
            raise Exception("无法从URL下载图片")

        # 从URL中获取文件名
        filename = os.path.basename(urlparse(url).path)
        if not filename:
            filename = 'downloaded_media'

        # 检查是否需要转换为webp
        convert_to_webp = data.pop('convert_to_webp', True)
        webp_quality = data.pop('webp_quality', 80)

        if convert_to_webp:
            # 读取图片数据
            image_data = io.BytesIO(response.content)
            try:
                # 打开图片
                with Image.open(image_data) as img:
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(suffix='.webp', delete=False) as temp_file:
                        # 转换并保存为webp格式
                        img.save(temp_file.name, 'WEBP', quality=webp_quality, optimize=True)
                        filename = os.path.splitext(filename)[0] + str(time.time_ns()) + '.webp'

                        # 准备媒体数据
                        media_data = {
                            'file': (filename, open(temp_file.name, 'rb'), 'image/webp'),
                            **data
                        }

                        # 使用multipart/form-data上传文件
                        url = f"{self.base_url}wp/v2/{self.endpoint}"
                        headers = self._get_headers()
                        if 'Content-Type' in headers:
                            del headers['Content-Type']

                        response = self.wordpress.session.post(
                            url,
                            headers=headers,
                            files={'file': media_data['file']},
                            data={k: v for k, v in media_data.items() if k != 'file'}
                        )

                        # 删除临时文件
                        os.unlink(temp_file.name)

                        return self._handle_response(response)
            except Exception as e:
                raise Exception(f"图片处理失败: {str(e)}")
        else:
            # 不需要转换，直接上传原始文件
            media_data = {
                'file': (filename, response.raw, response.headers.get('content-type')),
                **data
            }

            # 使用multipart/form-data上传文件
            url = f"{self.base_url}wp/v2/{self.endpoint}"
            headers = self._get_headers()
            if 'Content-Type' in headers:
                del headers['Content-Type']

            response = self.wordpress.session.post(
                url,
                headers=headers,
                files={'file': media_data['file']},
                data={k: v for k, v in media_data.items() if k != 'file'}
            )

            return self._handle_response(response)
