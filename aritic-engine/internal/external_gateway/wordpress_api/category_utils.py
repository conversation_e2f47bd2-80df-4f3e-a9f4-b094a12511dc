import os
import requests
from PIL import Image, ImageDraw, ImageFont
from io import Bytes<PERSON>
from random import randint
from internal.external_gateway.wordpress_api import WordPress


def generate_text_icon(text, size=(400, 400)):
    """
    生成基于文字的图标

    Args:
        text (str): 要显示的文字
        size (tuple): 图片尺寸

    Returns:
        BytesIO: 包含生成图片的缓冲区
    """
    # 创建图片
    image = Image.new('RGB', size, color=(randint(200, 255), randint(200, 255), randint(200, 255)))
    draw = ImageDraw.Draw(image)

    # 计算文字大小
    font_size = min(size[0], size[1]) // 4
    try:
        font = ImageFont.truetype('/System/Library/Fonts/Supplemental/Arial.ttf', font_size)
    except:
        font = ImageFont.load_default()

    # 获取文字的第一个字符（如果是中文）或前两个字符（如果是英文）
    if any('\u4e00' <= char <= '\u9fff' for char in text):
        display_text = text[0]
    else:
        display_text = text[:2].upper()

    # 计算文字位置使其居中
    text_bbox = draw.textbbox((0, 0), display_text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2

    # 绘制文字
    draw.text((x, y), display_text, font=font, fill=(50, 50, 50))

    # 保存为WebP格式
    webp_buffer = BytesIO()
    image.save(webp_buffer, 'WEBP', quality=85, optimize=True)
    webp_buffer.seek(0)
    return webp_buffer


def create_categories_with_images(wp, categories_data):
    """
    批量创建分类并设置特色图片

    Args:
        wp: WordPress API客户端实例
        categories_data (list): 包含分类信息的列表，每个元素应该是一个字典，包含name

    Returns:
        list: 创建的分类列表
    """
    created_categories = []

    for category_info in categories_data:
        category_name = category_info['name']
        category_description = category_info['description']
        try:
            # 生成文字图标
            webp_buffer = generate_text_icon(category_name)

            # 上传图片到媒体库
            # media = wp.media.create({
            #     'title': f'Category Icon - {category_name}',
            #     'alt_text': f'Icon for category {category_name}'
            # }, webp_buffer)

            # 创建分类
            new_category = wp.categories.create({
                'name': category_name,
                'slug': str(category_name.lower().replace(' & ', '-').replace(' ', '-')),
                'description': category_description,
                # 'meta': {
                #     'icon': media['id']
                # }
            })

            created_categories.append(new_category)
            print(f"Successfully created category: {category_name} with text icon")

        except Exception as e:
            print(f"Error processing category {category_name}: {str(e)}")
            continue

    return created_categories


if __name__ == '__main__':
    # 初始化WordPress API客户端
    wp = WordPress(
        url='https://smartreviews.top/wp-json/',
        username='AffMarketer',
        password='S1(DnB9o0o63E9ch1r'
    )

    # 准备分类数据
    categories_data = [
        {"name": "Arts & Entertainment", "description": "<div class='category-description'><h3>🎭 Arts & Entertainment</h3><hr class='fancy-hr'><p>✨ Discover the latest in <span class='highlight'>arts, entertainment, music, movies, theater</span>, and cultural events.</p><p>🎨 Your comprehensive guide to <em>creative expression</em> and <em>entertainment experiences</em>.</p></div>"},
        {"name": "Autos & Vehicles", "description": "<div class='category-description'><h3>🚗 Autos & Vehicles</h3><hr class='fancy-hr'><p>🔧 Expert reviews, buying guides, and maintenance tips for <span class='highlight'>cars, motorcycles, trucks</span>, and all types of vehicles.</p><p>🚘 Your trusted source for <em>automotive information</em>.</p></div>"},
        {"name": "Beauty & Fitness", "description": "<div class='category-description'><h3>💄 Beauty & Fitness</h3><hr class='fancy-hr'><p>💪 Complete resources for <span class='highlight'>beauty tips, skincare routines, fitness programs</span>, and wellness advice.</p><p>✨ Transform your lifestyle with <em>expert beauty and health guidance</em>.</p></div>"},
        {"name": "Books & Literature", "description": "<div class='category-description'><h3>📚 Books & Literature</h3><hr class='fancy-hr'><p>📖 Explore <span class='highlight'>literary works, book reviews, reading recommendations</span>, and author insights.</p><p>📝 Your gateway to the world of <em>literature and written expression</em>.</p></div>"},
        {"name": "Business & Industrial", "description": "<div class='category-description'><h3>💼 Business & Industrial</h3><hr class='fancy-hr'><p>📊 Professional insights into <span class='highlight'>business operations, industrial solutions</span>, and corporate strategies.</p><p>🏢 Essential resources for <em>business growth and industrial innovation</em>.</p></div>"},
        {"name": "Computers & Electronics", "description": "<div class='category-description'><h3>💻 Computers & Electronics</h3><hr class='fancy-hr'><p>🔌 Latest tech reviews, gadget comparisons, and expert advice on <span class='highlight'>computers, smartphones</span>, and electronic devices.</p><p>🌐 Stay updated with <em>cutting-edge technology</em>.</p></div>"},
        {"name": "Finance", "description": "<div class='category-description'><h3>💰 Finance</h3><hr class='fancy-hr'><p>📈 Expert financial advice, investment strategies, and <span class='highlight'>banking solutions</span>.</p><p>💹 Your guide to <em>personal and business financial success</em>.</p></div>"},
        {"name": "Food & Drink", "description": "<div class='category-description'><h3>🍽️ Food & Drink</h3><hr class='fancy-hr'><p>🍳 Culinary inspiration, recipes, restaurant reviews, and <span class='highlight'>beverage guides</span>.</p><p>🥂 Discover the world of <em>gastronomy and fine dining experiences</em>.</p></div>"},
        {"name": "Games", "description": "<div class='category-description'><h3>🎮 Games</h3><hr class='fancy-hr'><p>🎲 Reviews, strategies, and news about <span class='highlight'>video games, board games</span>, and interactive entertainment.</p><p>🏆 Your source for <em>gaming excitement and community</em>.</p></div>"},
        {"name": "Health", "description": "<div class='category-description'><h3>🏥 Health</h3><hr class='fancy-hr'><p>❤️ Comprehensive health information, medical advice, and <span class='highlight'>wellness tips</span>.</p><p>🌟 Your guide to <em>optimal physical and mental well-being</em>.</p></div>"},
        {"name": "Hobbies & Leisure", "description": "<div class='category-description'><h3>🎨 Hobbies & Leisure</h3><hr class='fancy-hr'><p>🎯 Explore diverse hobbies, recreational activities, and <span class='highlight'>leisure pursuits</span>.</p><p>✨ Find inspiration and guidance for your <em>favorite pastimes and interests</em>.</p></div>"},
        {"name": "Home & Garden", "description": "<div class='category-description'><h3>🏡 Home & Garden</h3><hr class='fancy-hr'><p>🌺 Home improvement tips, gardening advice, and <span class='highlight'>interior design inspiration</span>.</p><p>🏠 Transform your living space into a <em>beautiful sanctuary</em>.</p></div>"},
        {"name": "Internet & Telecom", "description": "<div class='category-description'><h3>🌐 Internet & Telecom</h3><hr class='fancy-hr'><p>📡 Updates on internet services, telecommunications, and <span class='highlight'>networking solutions</span>.</p><p>🔗 Stay connected with <em>latest communication technologies</em>.</p></div>"},
        {"name": "Jobs & Education", "description": "<div class='category-description'><h3>👨‍🎓 Jobs & Education</h3><hr class='fancy-hr'><p>📚 Career guidance, job opportunities, and <span class='highlight'>educational resources</span>.</p><p>🎓 Your path to <em>academic and career success</em>.</p></div>"},
        {"name": "Law & Government", "description": "<div class='category-description'><h3>⚖️ Law & Government</h3><hr class='fancy-hr'><p>📜 Legal information, government policies, and <span class='highlight'>regulatory updates</span>.</p><p>🏛️ Understanding your <em>rights and governmental processes</em>.</p></div>"},
        {"name": "News", "description": "<div class='category-description'><h3>📰 News</h3><hr class='fancy-hr'><p>📺 Breaking news, current events, and <span class='highlight'>in-depth reporting</span>.</p><p>🌍 Stay informed with <em>reliable news coverage</em>.</p></div>"},
        {"name": "Online Communities", "description": "<div class='category-description'><h3>👥 Online Communities</h3><hr class='fancy-hr'><p>🤝 Connect with like-minded individuals, forums, and <span class='highlight'>social networks</span>.</p><p>💫 Build <em>meaningful relationships in the digital world</em>.</p></div>"},
        {"name": "People & Society", "description": "<div class='category-description'><h3>👨‍👩‍👧‍👦 People & Society</h3><hr class='fancy-hr'><p>🌈 Insights into social issues, cultural perspectives, and <span class='highlight'>community matters</span>.</p><p>🤝 Understanding <em>human interactions and societal dynamics</em>.</p></div>"},
        {"name": "Pets & Animals", "description": "<div class='category-description'><h3>🐾 Pets & Animals</h3><hr class='fancy-hr'><p>🐱 Pet care guides, animal welfare information, and <span class='highlight'>veterinary advice</span>.</p><p>🦮 Everything for <em>animal lovers and pet owners</em>.</p></div>"},
        {"name": "Real Estate", "description": "<div class='category-description'><h3>🏘️ Real Estate</h3><hr class='fancy-hr'><p>🏠 Property listings, market analysis, and <span class='highlight'>buying guides</span>.</p><p>🔑 Your comprehensive resource for <em>real estate decisions</em>.</p></div>"},
        {"name": "Reference", "description": "<div class='category-description'><h3>📚 Reference</h3><hr class='fancy-hr'><p>📖 Reliable reference materials, educational resources, and <span class='highlight'>research tools</span>.</p><p>✏️ Your source for <em>accurate and verified information</em>.</p></div>"},
        {"name": "Science", "description": "<div class='category-description'><h3>🔬 Science</h3><hr class='fancy-hr'><p>🧬 Scientific discoveries, research updates, and <span class='highlight'>technological innovations</span>.</p><p>🔭 Exploring the wonders of <em>scientific advancement</em>.</p></div>"},
        {"name": "Sports", "description": "<div class='category-description'><h3>⚽ Sports</h3><hr class='fancy-hr'><p>🏅 Sports news, game analysis, and <span class='highlight'>athlete profiles</span>.</p><p>🎯 Your complete guide to <em>sporting events and athletic achievement</em>.</p></div>"},
        {"name": "Travel & Transportation", "description": "<div class='category-description'><h3>✈️ Travel & Transportation</h3><hr class='fancy-hr'><p>🌎 Travel guides, destination reviews, and <span class='highlight'>journey planning resources</span>.</p><p>🚆 Your companion for <em>memorable travel experiences</em>.</p></div>"},
        {"name": "World Localities", "description": "<div class='category-description'><h3>🌍 World Localities</h3><hr class='fancy-hr'><p>🗺️ Explore different regions, local cultures, and <span class='highlight'>community information</span>.</p><p>🌏 Understanding our <em>diverse world and its localities</em>.</p></div>"},
        {"name": "Antiques & Collectibles", "description": "<div class='category-description'><h3>🏺 Antiques & Collectibles</h3><hr class='fancy-hr'><p>🎭 Vintage items, collectibles, and <span class='highlight'>rare finds</span>.</p><p>💎 Expert guidance for <em>collectors and antique enthusiasts</em>.</p></div>"},
        {"name": "Apparel", "description": "<div class='category-description'><h3>👔 Apparel</h3><hr class='fancy-hr'><p>👗 Fashion trends, clothing reviews, and <span class='highlight'>style guides</span>.</p><p>👚 Your source for <em>fashion inspiration and clothing choices</em>.</p></div>"},
        {"name": "Auctions", "description": "<div class='category-description'><h3>🔨 Auctions</h3><hr class='fancy-hr'><p>💫 Online and live auction information, <span class='highlight'>bidding strategies</span>.</p><p>🎯 Your guide to <em>successful auction participation</em>.</p></div>"},
        {"name": "Classifieds", "description": "<div class='category-description'><h3>📢 Classifieds</h3><hr class='fancy-hr'><p>📋 Local listings, marketplace opportunities, and <span class='highlight'>advertisements</span>.</p><p>🤝 Connect with <em>buyers and sellers in your community</em>.</p></div>"},
        {"name": "Consumer Resources", "description": "<div class='category-description'><h3>🛒 Consumer Resources</h3><hr class='fancy-hr'><p>📊 Shopping guides, product reviews, and <span class='highlight'>buying advice</span>.</p><p>💡 Make <em>informed consumer decisions</em>.</p></div>"},
        {"name": "Discount & Outlet Stores", "description": "<div class='category-description'><h3>🏷️ Discount & Outlet Stores</h3><hr class='fancy-hr'><p>💰 Best deals, savings opportunities, and <span class='highlight'>shopping guides</span>.</p><p>🛍️ Smart shopping for <em>budget-conscious consumers</em>.</p></div>"},
        {"name": "Entertainment Media", "description": "<div class='category-description'><h3>🎬 Entertainment Media</h3><hr class='fancy-hr'><p>📺 Digital entertainment, streaming services, and <span class='highlight'>media reviews</span>.</p><p>🎥 Your guide to <em>quality entertainment options</em>.</p></div>"},
        {"name": "Gifts & Special Event Items", "description": "<div class='category-description'><h3>🎁 Gifts & Special Events</h3><hr class='fancy-hr'><p>🎈 Gift ideas, special occasion shopping, and <span class='highlight'>celebration essentials</span>.</p><p>🎊 Perfect presents for <em>every occasion</em>.</p></div>"},
        {"name": "Green & Eco-Friendly Shopping", "description": "<div class='category-description'><h3>🌱 Green & Eco-Friendly</h3><hr class='fancy-hr'><p>♻️ Sustainable products, eco-friendly alternatives, and <span class='highlight'>environmental guides</span>.</p><p>🌿 Shop responsibly for <em>our planet</em>.</p></div>"},
        {"name": "Luxury Goods", "description": "<div class='category-description'><h3>💎 Luxury Goods</h3><hr class='fancy-hr'><p>✨ Premium products, luxury brands, and <span class='highlight'>high-end shopping guides</span>.</p><p>👑 Experience the <em>finest in luxury consumption</em>.</p></div>"},
        {"name": "Mass Merchants & Department Stores", "description": "<div class='category-description'><h3>🏬 Mass Merchants</h3><hr class='fancy-hr'><p>🛍️ Retail shopping guides, store comparisons, and <span class='highlight'>merchandise reviews</span>.</p><p>🏪 Your comprehensive <em>retail shopping resource</em>.</p></div>"},
        {"name": "Photo & Video Services", "description": "<div class='category-description'><h3>📸 Photo & Video</h3><hr class='fancy-hr'><p>🎥 Professional photography, videography services, and <span class='highlight'>equipment reviews</span>.</p><p>📹 Capture your moments with <em>expert guidance</em>.</p></div>"},
        {"name": "Shopping Portals", "description": "<div class='category-description'><h3>🛒 Shopping Portals</h3><hr class='fancy-hr'><p>🔍 Online shopping platforms, e-commerce guides, and <span class='highlight'>marketplace comparisons</span>.</p><p>💻 Navigate the <em>digital shopping landscape</em> effectively.</p></div>"},
        {"name": "Swap Meets & Outdoor Markets", "description": "<div class='category-description'><h3>🏪 Swap Meets & Markets</h3><hr class='fancy-hr'><p>🌞 Flea market guides, outdoor shopping events, and <span class='highlight'>trading opportunities</span>.</p><p>🎪 Discover <em>unique items and local market experiences</em>.</p></div>"},
        {"name": "Toys", "description": "<div class='category-description'><h3>🧸 Toys</h3><hr class='fancy-hr'><p>🎪 Toy reviews, children's products, and <span class='highlight'>educational playthings</span>.</p><p>🎮 Find the perfect toys for <em>entertainment and development</em>.</p></div>"},
        {"name": "Wholesalers & Liquidators", "description": "<div class='category-description'><h3>📦 Wholesalers & Liquidators</h3><hr class='fancy-hr'><p>🏭 Bulk buying opportunities, wholesale markets, and <span class='highlight'>liquidation deals</span>.</p><p>💼 Source products <em>efficiently for business or personal needs</em>.</p></div>"}
    ]

    # 批量创建分类并设置特色图片
    created_categories = create_categories_with_images(wp, categories_data)
    print(f"Successfully created {len(created_categories)} categories with images.")
