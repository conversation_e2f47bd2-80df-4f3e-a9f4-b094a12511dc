import base64

class Auth:
    """
    处理WordPress API认证的类
    
    使用WordPress 5.6+版本的应用程序密码进行基本认证。
    应用程序密码可以在WordPress后台用户编辑页面生成（wp-admin -> Users -> Edit User）。
    认证格式与curl --user "USERNAME:PASSWORD"完全一致。
    
    Args:
        username (str, optional): WordPress用户名
        password (str, optional): WordPress应用程序密码或应用程序密码
    """
    
    def __init__(self, username=None, password=None):
        self.username = username
        self.password = password
    
    def has_credentials(self):
        """
        检查是否有认证凭据
        
        Returns:
            bool: 是否有认证凭据
        """
        return bool(self.username and self.password)
    
    def get_username(self):
        """
        获取用户名
        
        Returns:
            str: 用户名
        """
        return self.username
    
    def get_password(self):
        """
        获取密码
        
        Returns:
            str: 密码
        """
        return self.password
    
    def get_auth_header(self):
        """
        获取认证头信息
        
        Returns:
            dict: 包含认证信息的HTTP头字典
        """
        auth_str = f"{self.username}:{self.password}"
        auth_bytes = auth_str.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

        return {
            'Authorization': f'Basic {auth_b64}',
            'Content-Type': 'application/json'
        }
