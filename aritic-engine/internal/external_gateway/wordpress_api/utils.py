def create_post_with_meta(wp, post_data, category_name=None, tag_names=None, featured_image_url=None):
    """
    创建文章的完整流程，包括处理分类、标签和特色图片

    Args:
        wp: WordPress API客户端实例
        post_data (dict): 文章数据
        category_name (str, optional): 分类名称
        tag_names (list, optional): 标签名称列表
        featured_image_url (str, optional): 特色图片URL

    Returns:
        dict: 创建的文章详情
    """
    # 处理分类
    if category_name:
        # 获取所有分类
        categories = wp.categories.list({'per_page': 100})
        category_id = None

        # 查找是否存在该分类
        for category in categories:
            if category['name'] == category_name:
                category_id = category['id']
                break

        # 如果分类不存在，创建新分类
        if not category_id:
            new_category = wp.categories.create({
                'name': category_name,
                'slug': category_name.lower().replace(' & ', '-').replace(' ', '-')
            })
            category_id = new_category['id']

        post_data['categories'] = [category_id]

    # 处理标签
    if tag_names:
        tag_ids = []
        existing_tags = wp.tags.list({'per_page': 100})
        existing_tag_dict = {tag['name']: tag['id'] for tag in existing_tags}

        for tag_name in tag_names:
            # 检查标签是否存在
            if tag_name in existing_tag_dict:
                tag_ids.append(existing_tag_dict[tag_name])
            else:
                # 创建新标签
                new_tag = wp.tags.create({
                    'name': tag_name,
                    'slug': tag_name.lower().replace(' & ', '-').replace(' ', '-')
                })
                tag_ids.append(new_tag['id'])

        post_data['tags'] = tag_ids

    # 处理特色图片
    if featured_image_url:
        # 上传图片到媒体库
        media = wp.media.create({
            'title': 'Featured Image',
        }, featured_image_url)
        post_data['featured_media'] = media['id']

    # 创建文章
    return wp.posts.create(post_data)


def title_to_slug(title):
    # 转换为小写
    slug = title.lower()

    # 替换特殊字符为空格
    special_chars = "!@#$%^&*()_+=[]{}|;:,.<>?`~"
    for char in special_chars:
        slug = slug.replace(char, " ")

    # 将多个空格替换为单个连字符
    slug = "-".join(word for word in slug.split() if word)

    # 移除首尾的连字符
    slug = slug.strip("-")

    return slug


# 测试示例
if __name__ == "__main__":
    test_titles = [
        "Hello World!",
        "This is a Test Title",
        "Multiple   Spaces   Here",
        "Special@#Characters&Here",
    ]

    for title in test_titles:
        print(f"Original: {title}")
        print(f"Slug: {title_to_slug(title)}\n")