#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import logging
import random
from typing import Dict, List, Any, Optional
from datetime import datetime

from internal.storage.sheets import SheetService
from internal.storage.models import MerchantInfo, SiteConfig, ProxyConfig, ArticleStatus
from internal.generator.integrator import Integrator
from internal.publisher.publisher import WordPressPublisher, CustomAPIPublisher, PostgresPublisher, CouponsPostgresPublisher
from internal.external_gateway.brandfetchlib import brandfetchclient

logger = logging.getLogger(__name__)


class AriticEngine:
    """Aritic Engine主程序类，负责协调整个文章生成和发布流程"""
    
    def __init__(self, credentials_path: str, spreadsheet_name: str):
        """初始化Aritic Engine
        
        Args:
            credentials_path: Google API凭证文件路径
            spreadsheet_name: Google表格名称
        """
        try:
            # 加载Google API凭证
            with open(credentials_path, 'r') as f:
                credentials_dict = json.load(f)
                
            # 初始化SheetService
            self.sheet_service = SheetService(credentials_dict, spreadsheet_name)
            logger.info("成功初始化SheetService")
            
            # 初始化Integrator
            self.integrator = Integrator()
            logger.info("成功初始化Integrator")
            
            # 加载数据
            self.merchant_info_list = self._load_merchant_info()
            self.site_config_list = self._load_site_config()
            self.proxy_config_list = self._load_proxy_config()
            self.article_status_list = self._load_article_status()
            logger.info(f"成功加载数据: {len(self.merchant_info_list)}个商家信息, "
                       f"{len(self.site_config_list)}个站点配置, "
                       f"{len(self.proxy_config_list)}个代理配置, "
                       f"{len(self.article_status_list)}个文章状态")
        except Exception as e:
            logger.error(f"初始化Aritic Engine失败: {str(e)}")
            raise
    
    def _load_merchant_info(self) -> List[MerchantInfo]:
        """加载商家信息"""
        data = self.sheet_service.get_merchant_info()
        merchant_list = [MerchantInfo.from_dict(item) for item in data]

        # 按 official_url 去重，如果多个品牌有相同的官网，只保留一个
        return self._deduplicate_merchants_by_url(merchant_list)

    def _deduplicate_merchants_by_url(self, merchants: List[MerchantInfo]) -> List[MerchantInfo]:
        """按官网URL去重商家信息

        Args:
            merchants: 原始商家信息列表

        Returns:
            去重后的商家信息列表
        """
        seen_urls = set()
        deduplicated_merchants = []

        for merchant in merchants:
            # 标准化URL（去除协议和尾部斜杠）
            normalized_url = merchant.official_url.lower().strip()
            if normalized_url.startswith('http://'):
                normalized_url = normalized_url[7:]
            elif normalized_url.startswith('https://'):
                normalized_url = normalized_url[8:]
            normalized_url = normalized_url.rstrip('/')

            if normalized_url not in seen_urls:
                seen_urls.add(normalized_url)
                deduplicated_merchants.append(merchant)
                logger.info(f"保留商家: {merchant.brand_name} (URL: {merchant.official_url})")
            else:
                logger.info(f"去重商家: {merchant.brand_name} (URL: {merchant.official_url}) - URL已存在")

        logger.info(f"去重完成: 原始 {len(merchants)} 个商家，去重后 {len(deduplicated_merchants)} 个商家")
        return deduplicated_merchants
    
    def _load_site_config(self) -> List[SiteConfig]:
        """加载站点配置"""
        data = self.sheet_service.get_site_config()
        return [SiteConfig.from_dict(item) for item in data]
    
    def _load_proxy_config(self) -> List[ProxyConfig]:
        """加载代理配置"""
        data = self.sheet_service.get_proxy_config()
        return [ProxyConfig.from_dict(item) for item in data]
    
    def _load_article_status(self) -> List[ArticleStatus]:
        """加载文章状态"""
        data = self.sheet_service.get_article_status()
        return [ArticleStatus.from_dict(item) for item in data]
    
    def _get_random_proxy_info(self) -> Optional[str]:
        """随机获取一个代理信息
        
        Returns:
            Optional[str]: 代理URL字符串，如果没有代理则返回None
        """
        if not self.proxy_config_list:
            return None
        
        # 随机选择一个代理配置
        proxy = random.choice(self.proxy_config_list)
        
        if proxy.username and proxy.password:
            return f"{proxy.type}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}"
        else:
            return f"{proxy.type}://{proxy.host}:{proxy.port}"
    
    def _is_article_published(self, merchant_info: MerchantInfo, site_id: str) -> bool:
        """检查文章是否已发布
        
        Args:
            merchant_info: 商家信息
            site_id: 站点ID
            
        Returns:
            bool: 是否已发布
        """
        for status in self.article_status_list:
            if (status.brand_slug == merchant_info.brand_slug and 
                status.official_url == merchant_info.official_url and 
                status.site_id == site_id and 
                status.status == "published"):
                return True
        return False
    
    def _get_site_config(self, site_id: str) -> Optional[SiteConfig]:
        """获取站点配置
        
        Args:
            site_id: 站点ID
            
        Returns:
            SiteConfig: 站点配置
        """
        for site_config in self.site_config_list:
            if site_config.site_id == site_id:
                return site_config
        return None
    
    def _process_merchant(self, merchant_info: MerchantInfo) -> bool:
        """处理单个商家信息
        
        Args:
            merchant_info: 商家信息
            
        Returns:
            bool: 处理是否成功
        """
        # 检查是否已发布
        if self._is_article_published(merchant_info, merchant_info.site_id):
            logger.info(f"商家 {merchant_info.brand_name} 在站点 {merchant_info.site_id} 已发布，跳过")
            return True
        
        # 获取站点配置
        site_config = self._get_site_config(merchant_info.site_id)
        if not site_config:
            logger.error(f"找不到站点配置: {merchant_info.site_id}，跳过")
            return False
        
        try:
            # 为每个商家随机获取一个代理
            proxy_info = self._get_random_proxy_info()
            
            # 生成文章
            article_data = self._generate_article(merchant_info, proxy_info)
            if not article_data:
                return False
            
            # 发布文章
            success = self._publish_article(merchant_info, site_config, article_data)
            return success
        except Exception as e:
            logger.error(f"处理商家 {merchant_info.brand_name} 时出错: {str(e)}")
            return False
    
    def _generate_article(self, merchant_info: MerchantInfo, proxy_info: Optional[str]):
        """生成文章
        
        Args:
            merchant_info: 商家信息
            proxy_info: 代理信息
            
        Returns:
            生成的文章数据
        """
        logger.info(f"生成文章: {merchant_info.brand_name}")
        try:
            return self.integrator.generate_article(
                site=merchant_info.official_url,
                affiliate_url=merchant_info.affiliate_url,
                proxy_info=proxy_info
            )
        except Exception as e:
            logger.error(f"生成文章失败: {merchant_info.brand_name}, 错误: {str(e)}")
            return None
    
    def _publish_article(self, merchant_info: MerchantInfo, site_config: SiteConfig, article_data):
        """发布文章
        
        Args:
            merchant_info: 商家信息
            site_config: 站点配置
            article_data: 文章数据
            
        Returns:
            bool: 发布是否成功
        """
        logger.info(f"发布文章: {merchant_info.brand_name} 到 {site_config.site_url}")
        try:
            # 根据站点类型选择发布器
            if site_config.site_type == "wordpress":
                publisher = WordPressPublisher(site_config)
            elif site_config.site_type == "custom":
                publisher = CustomAPIPublisher(site_config)
            elif site_config.site_type == "postgres":
                publisher = PostgresPublisher(site_config)
            elif site_config.site_type == "couponspostgres":
                publisher = CouponsPostgresPublisher(site_config)
            else:
                logger.error(f"不支持的站点类型: {site_config.site_type}")
                return False
            # 发布前校验
            image_url = article_data.website_screenshot
            if len(article_data.images) > 0:
                image_url = article_data.images[0].url
            merchant_info.brand_name = brandfetchclient.fetch_brand_data(merchant_info.official_url, merchant_info.brand_name)
            success = publisher.publish_article(
                title=article_data.title,
                content=article_data.content,
                publish_date=str(merchant_info.publish_date),
                merchant_info=merchant_info,
                logo=article_data.website_logo,
                brand_description=article_data.meta_description,
                excerpt=article_data.meta_description,
                image_url=image_url,
                category_name=article_data.category_name,
                tag_names=article_data.tags,
            )
            
            # 更新状态
            if success:
                logger.info(f"发布成功: {merchant_info.brand_name}")
                self.sheet_service.update_article_status(
                    brand_slug=merchant_info.brand_slug,
                    official_url=merchant_info.official_url,
                    site_id=merchant_info.site_id,
                    status="published"
                )
                return True
            else:
                logger.error(f"发布失败: {merchant_info.brand_name}")
                return False
        except Exception as e:
            logger.error(f"发布文章失败: {merchant_info.brand_name}, 错误: {str(e)}")
            return False
    
    def run(self):
        """运行Aritic Engine"""
        # 处理每个商家信息
        success_count = 0
        total_count = len(self.merchant_info_list)
        
        for merchant_info in self.merchant_info_list:
            if self._process_merchant(merchant_info):
                success_count += 1
        
        logger.info(f"Aritic Engine运行完成，成功处理 {success_count}/{total_count} 个商家")